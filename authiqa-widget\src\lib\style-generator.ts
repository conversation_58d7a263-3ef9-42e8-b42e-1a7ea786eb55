import { WidgetCustomization } from './customization-types';
import { defaultCustomization } from './default-customization';

export class StyleGenerator {
  private customization: WidgetCustomization;

  constructor(customConfig?: Partial<WidgetCustomization>) {
    this.customization = {
      ...defaultCustomization,
      ...customConfig,
      typography: {
        ...defaultCustomization.typography,
        ...customConfig?.typography,
        titleText: {
          ...defaultCustomization.typography.titleText,
          ...customConfig?.typography?.titleText
        },
        subtitleText: {
          ...defaultCustomization.typography.subtitleText,
          ...customConfig?.typography?.subtitleText
        }
      }
    };
  }

  generateStyles(): string {
    const { layout, colors, typography, buttons } = this.customization;
    const isDarkTheme = colors.background && (colors.background.toLowerCase() === '#27272a' || colors.background.toLowerCase() === '#18181b');
    
    return `
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');
        
        .authiqa-container {
            background-color: ${colors.background};
            padding: ${layout.paddingTop || '1.25rem'} ${layout.padding} ${layout.padding} ${layout.padding};
            margin: ${layout.margin};
            border-radius: ${layout.borderRadius};
            max-width: ${layout.maxWidth};
            min-width: ${layout.minWidth || 'auto'};
            width: ${layout.width || 'auto'};
            height: ${layout.height || 'auto'};
            min-height: ${layout.minHeight || 'auto'};
            max-height: ${layout.maxHeight || 'auto'};
            font-family: ${typography.fontFamily};
            --authiqa-nav-text-color: ${typography.navTextColor || '#1a1a1a'};
            --authiqa-nav-text-color-dark: ${typography.navTextColorDark || '#ffffff'};
        }

        .authiqa-container h1 {
            color: ${typography.titleColor};
            font-size: ${typography.titleSize};
            margin-top: 0;
            margin-bottom: 2rem;
            text-align: ${typography.titleAlignment || 'center'};
            font-weight: ${typography.titleWeight || '600'};
            line-height: ${typography.titleLineHeight || '1.2'};
        }

        .authiqa-container input {
            background-color: ${colors.inputBackground};
            color: ${colors.inputText};
            border: 1px solid ${colors.borderColor};
            border-radius: ${this.customization.inputs?.borderRadius || '4px'};
            height: ${this.customization.inputs?.height || '50px'};
            width: ${this.customization.inputs?.width || '100%'};
            padding: ${this.customization.inputs?.padding || '0 1rem'};
            margin: ${this.customization.inputs?.margin || '0 0 1rem 0'};
            font-size: ${this.customization.inputs?.fontSize || '1rem'};
            font-weight: ${this.customization.inputs?.fontWeight || '400'};
            box-sizing: border-box;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        .authiqa-container input:focus {
            border-color: ${this.customization.inputs?.focusBorderColor || '#000000'};
            box-shadow: ${this.customization.inputs?.focusBoxShadow || 'none'};
            outline: none;
        }

        /* Make button selectors more specific to override defaults, but exclude GitHub buttons and password toggle */
        .authiqa-container button:not(.authiqa-github-button):not(.password-toggle),
        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle),
        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle) {
            background-color: ${colors.buttonBackground} !important;
            color: ${colors.buttonText} !important;
            height: ${buttons.height || '40px'} !important;
            width: ${buttons.width || '100%'} !important;
            border-radius: ${buttons.borderRadius} !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            line-height: 1 !important;
        }
        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle):hover,
        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle):hover {
            background-color: ${buttons.hoverBackground || colors.buttonBackground} !important;
        }

        /* GitHub button specific styling with proper interactivity */
        .authiqa-container .authiqa-github-button {
            background-color: #ffffff !important;
            color: #000000 !important;
            border: 1px solid #30363d !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem 0.5rem 0.9rem !important;
            font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            width: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            gap: 0.5rem !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            line-height: 1 !important;
        }
        .authiqa-container .authiqa-github-button:hover {
            background-color: #f6f8fa !important;
            border-color: #1f2328 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }
        .authiqa-container .authiqa-github-button:active {
            transform: scale(0.98) !important;
            box-shadow: none !important;
            background-color: #eaeef2 !important;
        }
        
        /* Google button styling - clean and simple like GitHub */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"],
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"] {
            background-color: #ffffff !important;
            color: #000000 !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 0.5rem 1rem 0.5rem 0.1rem !important;
            font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            width: 100% !important;
            max-width: 100% !important;
            min-width: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            gap: 0.5rem !important;
            cursor: pointer !important;
            transition: none !important;
            transform: none !important;
            line-height: 1 !important;
            box-sizing: border-box !important;
            height: 40px !important;
            margin: 0 !important;
            outline: none !important;
            box-shadow: none !important;
        }
        
        /* Remove all hover and active effects */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:hover,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:active,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:focus,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:hover,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:active,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:focus,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:hover,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:active,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:focus,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:hover,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:active,
        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:focus {
            background-color: #ffffff !important;
            transform: none !important;
            box-shadow: none !important;
            border: none !important;
            outline: none !important;
            transition: none !important;
        }
        
        /* Google button logo positioning */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb .nsm7Bb-HzV7m-LgbsSe,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb svg,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe svg {
            margin: 0 !important;
            padding: 0 !important;
            width: 20px !important;
            height: 20px !important;
            flex-shrink: 0 !important;
        }
        
        /* Google button text positioning */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb .nsm7Bb-HzV7m-LgbsSe-Bz112c,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb span,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe-Bz112c,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe span {
            margin: 0 !important;
            padding: 0 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #000000 !important;
            flex: none !important;
        }

        /* Personalized Google button specific styling to force full width */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe {
            min-width: 100% !important;
            max-width: 100% !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }

        /* Force personalized Google button profile image to proper size */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe img,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe {
            width: 20px !important;
            height: 20px !important;
            border-radius: 50% !important;
            flex-shrink: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        /* Aggressive Google styling removal */
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:hover,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:active,
        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:focus {
            box-shadow: none !important;
            border: none !important;
            outline: none !important;
            transition: none !important;
            transform: none !important;
            margin: 0 !important;
        }
        
        /* Label styling */
        .authiqa-container .authiqa-label,
        .authiqa-label {
            display: block !important;
            margin-bottom: 0.5rem !important;
            padding-left: 0.09rem !important;
            font-weight: ${typography.labelFontWeight || '400'} !important;
            color: ${typography.labelColor || (isDarkTheme ? '#ffffff' : (colors.inputText || '#333333'))} !important;
            font-size: ${typography.labelSize || '0.9rem'} !important;
            height: auto !important;
            line-height: 1.2 !important;
        }
        
        /* Dark theme specific styles */
        .authiqa-container[data-theme="dark"] .authiqa-label,
        .authiqa-container[data-theme="dark"] label {
            color: #ffffff !important;
        }

        .authiqa-container .authiqa-labeled-input {
            margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1.2rem (about 5px less) */
        }

        /* Ensure password container properly styles the label */
        .authiqa-container .authiqa-password-container .authiqa-label {
            display: block !important;
            margin-bottom: 0.3rem !important; /* Changed to 0.3rem (approximately 5px) */
            padding-left: 0.08rem !important; /* Added left padding to move labels slightly right */
            font-weight: 500 !important;
            height: 14px !important; /* Added fixed height */
            line-height: 14px !important; /* Added line height to match height */
        }

        /* Password field container */
        .authiqa-container .authiqa-password-container,
        .authiqa-container .password-field-container {
            position: relative !important;
            width: 100% !important;
        }

        /* Password toggle button */
        .authiqa-container .password-toggle {
            position: absolute !important;
            right: ${this.customization?.passwordToggle?.rightOffset || '12px'} !important;
            top: ${this.customization.inputs?.height ? `calc(${this.customization.inputs.height} / 2)` : '24px'} !important;
            transform: translateY(-50%) !important;
            background: none !important;
            border: none !important;
            color: ${this.customization?.passwordToggle?.color || colors.inputText ? colors.inputText + '99' : '#a1a1aa'} !important;
            cursor: pointer !important;
            padding: ${this.customization?.passwordToggle?.padding || '4px'} !important;
            margin: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 16px !important;
            width: 16px !important;
            z-index: 10 !important;
            font-size: ${this.customization?.passwordToggle?.size || '14px'} !important;
            line-height: 1 !important;
            border-radius: 2px !important;
        }

        .authiqa-container .password-toggle:hover {
            color: ${this.customization?.passwordToggle?.hoverColor || colors.inputText || '#000000'} !important;
        }
        
        /* Terms container - adjusted spacing */
        .authiqa-container .terms-container {
            display: flex !important;
            align-items: flex-start !important;
            margin: 0.5rem 0 1rem 0 !important; /* Decreased bottom margin from 3rem to 1rem */
            position: relative !important;
        }

        .authiqa-container .terms-container input[type="checkbox"] {
            margin: 0.25rem 0.5rem 0 0 !important; /* Standardized margins */
            position: static !important; /* Remove relative positioning */
        }

        .authiqa-container .terms-container label {
            color: ${typography.termsText?.textColor || colors.inputText || '#333333'} !important;
            font-size: 0.875rem !important;
            line-height: 1.4 !important;
            margin: 0 !important;
            padding-top: 0 !important;
            margin-left: 0 !important;
            flex: 1 !important;
        }
        
        .authiqa-container .terms-container a {
            color: ${typography.termsText?.linkColor || colors.buttonBackground || '#000000'} !important;
            text-decoration: none !important;
        }
        
        /* Input field styling - highest priority for user customization */
        .authiqa-container input[type="text"],
        .authiqa-container input[type="email"],
        .authiqa-container input[type="password"],
        .authiqa-input {
            width: ${this.customization.inputs?.width || '100%'} !important;
            height: ${this.customization.inputs?.height || '50px'} !important;
            padding: ${this.customization.inputs?.padding || '0 1rem'} !important;
            font-size: ${this.customization.inputs?.fontSize || '1rem'} !important;
            font-weight: ${this.customization.inputs?.fontWeight || '400'} !important;
            border-radius: ${this.customization.inputs?.borderRadius || '4px'} !important;
            background-color: ${colors.inputBackground} !important;
            color: ${colors.inputText} !important;
            border: 1px solid ${colors.borderColor} !important;
            margin: ${this.customization.inputs?.margin || '0 0 1rem 0'} !important;
            box-sizing: border-box !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }
        .authiqa-container input[type="text"]:focus,
        .authiqa-container input[type="email"]:focus,
        .authiqa-container input[type="password"]:focus,
        .authiqa-input:focus {
            border-color: ${this.customization.inputs?.focusBorderColor || '#000000'} !important;
            box-shadow: ${this.customization.inputs?.focusBoxShadow || 'none'} !important;
            outline: none !important;
        }

        /* Checkbox specific styling */
        .authiqa-container input[type="checkbox"] {
            width: auto !important;
            height: auto !important;
            margin-right: 8px !important;
            margin-top: 3px !important;
            background-color: transparent !important;
        }

        /* Decrease spacing between password field and terms */
        .authiqa-container .authiqa-labeled-input {
            margin-bottom: 0.5rem !important; /* Decreased from 1rem to 0.5rem */
        }

        /* Button spacing - no need to change as the terms container's bottom margin will create space */
        .authiqa-container form button[type="submit"] {
            margin-top: 0 !important; /* Remove top margin as we're using bottom margin on terms container */
        }

        .authiqa-container input[type="text"]::placeholder,
        .authiqa-container input[type="email"]::placeholder,
        .authiqa-container input[type="password"]::placeholder,
        .authiqa-input::placeholder {
            color: ${colors.inputPlaceholder || '#a3a3a3'} !important;
            text-align: ${this.customization.inputs?.placeholderAlign || 'left'} !important;
        }

        /* Navigation (alternate-action) styling */
        .authiqa-container .alternate-action {
            text-align: ${this.customization.navLinks?.textAlign || 'center'} !important;
            margin-top: ${this.customization.navLinks?.marginTop || '1.5rem'} !important;
            margin-bottom: ${this.customization.navLinks?.marginBottom || '0'} !important;
            font-size: ${this.customization.navLinks?.fontSize || '0.95rem'} !important;
            color: ${this.customization.navLinks?.color || 'var(--authiqa-nav-text-color, #1a1a1a)'} !important;
            font-family: ${this.customization.navLinks?.fontFamily || typography.fontFamily} !important;
            font-weight: ${this.customization.navLinks?.fontWeight || '400'} !important;
        }
        .authiqa-container[data-theme="dark"] .alternate-action {
            color: var(--authiqa-nav-text-color-dark, #ffffff) !important;
        }

        ${this.customization.navLinks?.secondaryButtonStyle ? `
        /* Secondary button styling for navigation links */
        .authiqa-container .alternate-action a {
            display: inline-block !important;
            background-color: ${this.customization.navLinks?.secondaryButtonBackground || '#42b883'} !important;
            color: ${this.customization.navLinks?.secondaryButtonText || '#ffffff'} !important;
            padding: ${this.customization.navLinks?.secondaryButtonPadding || '0.5rem 1rem'} !important;
            border-radius: ${this.customization.navLinks?.secondaryButtonBorderRadius || '6px'} !important;
            height: ${this.customization.navLinks?.secondaryButtonHeight || 'auto'} !important;
            text-decoration: none !important;
            font-weight: ${this.customization.navLinks?.linkFontWeight || '500'} !important;
            transition: background-color 0.2s ease !important;
            border: none !important;
            cursor: pointer !important;
            text-align: center !important;
            line-height: 1.2 !important;
            font-size: ${this.customization.navLinks?.fontSize || '0.875rem'} !important;
            min-width: auto !important;
            width: auto !important;
            box-sizing: border-box !important;
        }
        .authiqa-container .alternate-action a:hover {
            background-color: ${this.customization.navLinks?.secondaryButtonHover || '#369870'} !important;
            text-decoration: none !important;
        }
        ` : `
        /* Default link styling for navigation */
        .authiqa-container .alternate-action a {
            color: ${this.customization.navLinks?.linkColor || '#0070f3'} !important;
            font-weight: ${this.customization.navLinks?.linkFontWeight || '500'} !important;
            text-decoration: none !important;
        }
        .authiqa-container .alternate-action a:hover {
            text-decoration: underline !important;
        }
        `}
        /* Forgot password link styling */
        .authiqa-container .forgot-password {
            text-align: ${this.customization.navLinks?.textAlign || 'right'} !important;
            margin-top: -1rem !important;
            margin-bottom: 1rem !important;
            font-size: ${this.customization.navLinks?.fontSize || '0.95rem'} !important;
            color: ${this.customization.navLinks?.color || '#525252'} !important;
            font-family: ${this.customization.navLinks?.fontFamily || typography.fontFamily} !important;
            font-weight: ${this.customization.navLinks?.fontWeight || '400'} !important;
        }
        .authiqa-container .forgot-password a {
            color: ${this.customization.navLinks?.linkColor || '#0070f3'} !important;
            font-weight: ${this.customization.navLinks?.linkFontWeight || '500'} !important;
            text-decoration: none !important;
        }
        .authiqa-container .forgot-password a:hover {
            text-decoration: underline !important;
        }
    `;
  }
}














