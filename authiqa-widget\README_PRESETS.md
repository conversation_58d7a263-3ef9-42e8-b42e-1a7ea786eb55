# Authiqa Widget Preset System

The Authiqa widget now includes a comprehensive preset system that allows developers to quickly apply professionally designed themes to their authentication forms. This system provides pre-configured styling options while maintaining full customization flexibility.

## 🎨 What are Presets?

Presets are pre-designed styling configurations that include:
- Color schemes
- Typography settings
- Layout configurations
- Input field styling
- Button designs
- Navigation elements
- Spacing and sizing

Each preset is carefully crafted to provide a cohesive, professional appearance that works well across different use cases and brand personalities.

## 🚀 Quick Start

### Basic Usage
```javascript
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern', // Apply the modern preset
  organizationDomain: 'your-domain.com'
});

widget.show('signin');
```

### With Custom Overrides
```javascript
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'corporate',
  customization: {
    colors: {
      buttonBackground: '#your-brand-color'
    }
  },
  organizationDomain: 'your-domain.com'
});
```

## 📋 Available Presets

| Preset | Category | Description | Best For |
|--------|----------|-------------|----------|
| `modern` | Modern | Clean, contemporary design with subtle shadows | SaaS platforms, tech startups |
| `classic` | Traditional | Professional design with clean lines | Corporate websites, traditional businesses |
| `minimal` | Modern | Ultra-clean with maximum whitespace | Design agencies, portfolios |
| `corporate` | Professional | Formal business design with corporate colors | Enterprise applications, B2B platforms |
| `gradient` | Creative | Vibrant gradients and smooth transitions | Creative agencies, modern apps |
| `rounded` | Modern | Friendly design with rounded corners | Consumer apps, friendly brands |
| `sharp` | Modern | Bold, angular design with high contrast | Gaming, tech, edgy brands |

## 🔧 Dynamic Preset Management

### Change Presets Programmatically
```javascript
// Switch to a different preset
widget.applyPreset('minimal');

// Apply preset with custom overrides
widget.applyPreset('corporate', {
  colors: {
    buttonBackground: '#ff6b6b',
    buttonText: '#ffffff'
  },
  typography: {
    titleText: {
      signinText: 'Welcome to Our Platform'
    }
  }
});
```

### Get Available Presets
```javascript
const presets = widget.getAvailablePresets();
console.log(presets);
// Returns array of preset metadata
```

### Check Current Preset
```javascript
const currentPreset = widget.getCurrentPreset();
console.log(currentPreset); // e.g., 'modern'
```

## 🎯 Customization Priority

When using presets with custom overrides, the merge priority is:

1. **Preset Configuration** (base)
2. **Custom Overrides** (highest priority)

This means your custom settings will always override the preset's defaults for those specific properties.

## 📱 Framework Integration

### React Example
```jsx
import React, { useEffect, useState } from 'react';

const AuthComponent = () => {
  const [widget, setWidget] = useState(null);
  const [currentPreset, setCurrentPreset] = useState('modern');

  useEffect(() => {
    const authWidget = new AuthiqaWidget({
      publicKey: 'your-key',
      container: 'auth-container',
      mode: 'inline',
      preset: currentPreset,
      organizationDomain: 'your-domain.com'
    });
    
    authWidget.show('signin');
    setWidget(authWidget);
  }, []);

  const changePreset = (newPreset) => {
    if (widget) {
      widget.applyPreset(newPreset);
      setCurrentPreset(newPreset);
    }
  };

  return (
    <div>
      <select onChange={(e) => changePreset(e.target.value)}>
        <option value="modern">Modern</option>
        <option value="classic">Classic</option>
        <option value="minimal">Minimal</option>
      </select>
      <div id="auth-container"></div>
    </div>
  );
};
```

### Vue Example
```vue
<template>
  <div>
    <select @change="changePreset" v-model="currentPreset">
      <option value="modern">Modern</option>
      <option value="classic">Classic</option>
      <option value="minimal">Minimal</option>
    </select>
    <div id="auth-container"></div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      widget: null,
      currentPreset: 'modern'
    };
  },
  mounted() {
    this.widget = new AuthiqaWidget({
      publicKey: 'your-key',
      container: 'auth-container',
      mode: 'inline',
      preset: this.currentPreset,
      organizationDomain: 'your-domain.com'
    });
    this.widget.show('signin');
  },
  methods: {
    changePreset() {
      if (this.widget) {
        this.widget.applyPreset(this.currentPreset);
      }
    }
  }
};
</script>
```

## 🛠️ Advanced Usage

### Creating Custom Preset-like Configurations
```javascript
// Define your own preset-like configuration
const myCustomConfig = {
  colors: {
    background: '#f0f9ff',
    buttonBackground: '#0ea5e9',
    buttonText: '#ffffff',
    inputBackground: '#ffffff',
    inputText: '#0f172a',
    borderColor: '#e0f2fe'
  },
  typography: {
    fontFamily: '"Your Custom Font", sans-serif',
    titleColor: '#0c4a6e'
  }
  // ... other customizations
};

const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'authiqa',
  mode: 'inline',
  customization: myCustomConfig,
  organizationDomain: 'your-domain.com'
});
```

### Preset with Theme Switching
```javascript
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern',
  organizationDomain: 'your-domain.com'
});

// Switch between light and dark variants
const applyDarkMode = () => {
  widget.applyPreset('modern', {
    colors: {
      background: '#1f2937',
      inputBackground: '#374151',
      inputText: '#ffffff',
      borderColor: '#4b5563'
    }
  });
};
```

## 📚 Examples

- **HTML Example**: `examples/preset-examples.html`
- **React Example**: `examples/react-preset-example.jsx`
- **Full Documentation**: `PRESET_DESIGNS.md`

## 🔍 Browser Support

All presets are designed to work across modern browsers with graceful fallbacks for older browsers. The preset system uses CSS-in-JS generation for maximum compatibility.

## 🤝 Contributing

To add new presets:

1. Create preset configuration in `src/lib/preset-designs.ts` or `src/lib/preset-designs-extended.ts`
2. Add preset name to `PresetName` type in `src/lib/preset-types.ts`
3. Register preset in `src/lib/preset-manager.ts`
4. Update documentation

## 📄 License

The preset system is part of the Authiqa Widget and follows the same licensing terms.
