export interface WidgetCustomization {
    layout: {
        padding?: string;
        paddingTop?: string;
        margin?: string;
        borderRadius?: string;
        maxWidth?: string;
        minWidth?: string;
        width?: string;
        height?: string;
        minHeight?: string;
        maxHeight?: string;
    };
    colors: {
        background?: string;
        buttonBackground?: string;
        buttonText?: string;
        inputBackground?: string;
        inputText?: string;
        inputPlaceholder?: string;
        borderColor?: string;
    };
    typography: {
        titleText: {
            signinText?: string;
            signupText?: string;
            resetText?: string;
            updateText?: string;
            verifyText?: string;
            resendText?: string;
        };
        subtitleText?: {
            signinText?: string;
            signupText?: string;
            resetText?: string;
            updateText?: string;
            verifyText?: string;
            resendText?: string;
        };
        titleSize?: string;
        titleColor?: string;
        labelSize?: string;
        fontFamily?: string;
        labelColor?: string;
        labelFontWeight?: string;
        titleAlignment?: string;
        titleWeight?: string;
        titleLineHeight?: string;
        termsText: {
            agreePrefix: string;
            andConnector: string;
            defaultPrefix: string;
            linkText: {
                terms: string;
                privacy: string;
                notifications: string;
            };
            textColor?: string;
            linkColor?: string;
        };
        navTextColor?: string;
        navTextColorDark?: string;
    };
    inputs: {
        emailPlaceholder?: string;
        passwordPlaceholder?: string;
        usernamePlaceholder?: string;
        confirmPasswordPlaceholder?: string;
        emailLabel?: string;
        passwordLabel?: string;
        usernameLabel?: string;
        confirmPasswordLabel?: string;
        borderRadius?: string;
        height?: string;
        width?: string;
        padding?: string;
        margin?: string;
        fontSize?: string;
        fontWeight?: string;
        focusBorderColor?: string;
        focusBoxShadow?: string;
        placeholderAlign?: string;
    };
    buttons: {
        signinText?: string;
        signupText?: string;
        resetText?: string;
        updateText?: string;
        verifyText?: string;
        resendText?: string;
        height?: string;
        width?: string;
        padding?: string;
        margin?: string;
        borderRadius?: string;
        hoverBackground?: string;
    };
    navLinks?: {
        signinPrompt?: string;
        signinLinkText?: string;
        signupPrompt?: string;
        signupLinkText?: string;
        forgotPrompt?: string;
        forgotLinkText?: string;
        fontSize?: string;
        color?: string;
        fontFamily?: string;
        textAlign?: string;
        marginTop?: string;
        marginBottom?: string;
        fontWeight?: string;
        linkColor?: string;
        linkFontWeight?: string;
        backToSigninPrompt?: string;
        secondaryButtonStyle?: boolean;
        secondaryButtonBackground?: string;
        secondaryButtonText?: string;
        secondaryButtonHover?: string;
        secondaryButtonBorderRadius?: string;
        secondaryButtonHeight?: string;
        secondaryButtonPadding?: string;
    };
    pageLayout?: {
        formPosition?: 'center' | 'left' | 'right' | 'top' | 'bottom';
        formMarginTop?: string;
        formMarginBottom?: string;
        formMarginLeft?: string;
        formMarginRight?: string;
    };
    passwordToggle?: {
        position?: 'right' | 'center';
        rightOffset?: string;
        centerOffset?: string;
        color?: string;
        hoverColor?: string;
        size?: string;
        padding?: string;
    };
}
