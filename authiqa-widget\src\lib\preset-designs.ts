import { PresetDesign } from './preset-types';

export const modernPreset: PresetDesign = {
  name: 'modern',
  displayName: 'Modern',
  description: 'Clean, contemporary design with subtle shadows and modern typography',
  category: 'modern',
  customization: {
    layout: {
      padding: '2rem',
      paddingTop: '1.5rem',
      margin: '1rem',
      borderRadius: '12px',
      maxWidth: '420px',
      minWidth: '320px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#1f2937',
      buttonBackground: '#3b82f6',
      buttonText: '#ffffff',
      inputBackground: '#374151',
      inputText: '#ffffff',
      inputPlaceholder: '#9ca3af',
      borderColor: '#4b5563'
    },
    typography: {
      titleText: {
        signinText: 'Welcome Back',
        signupText: 'Create an account',
        resetText: 'Reset Password',
        updateText: 'Update Password',
        verifyText: 'Verify Email',
        resendText: 'Resend Confirmation'
      },
      subtitleText: {
        signinText: '',
        signupText: '',
        resetText: '',
        updateText: '',
        verifyText: '',
        resendText: ''
      },
      titleSize: '1.5rem',
      titleColor: '#ffffff',
      labelSize: '0.875rem',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      labelColor: '#ffffff',
      labelFontWeight: '500',
      titleAlignment: 'left',
      titleWeight: '600',
      titleLineHeight: '1.25',
      termsText: {
        agreePrefix: 'I agree to the',
        andConnector: 'and',
        defaultPrefix: 'default',
        linkText: {
          terms: 'Terms of Service',
          privacy: 'Privacy Policy',
          notifications: 'Notification Settings'
        },
        textColor: '#d1d5db',
        linkColor: '#3b82f6'
      },
      navTextColor: '#d1d5db',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: '',
      passwordPlaceholder: '',
      usernamePlaceholder: '',
      confirmPasswordPlaceholder: '',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      usernameLabel: 'Username',
      confirmPasswordLabel: 'Confirm password',
      borderRadius: '6px',
      height: '48px',
      width: '100%',
      padding: '0 1rem',
      margin: '0 0 1.5rem 0',
      fontSize: '1rem',
      fontWeight: '400',
      focusBorderColor: '#3b82f6',
      focusBoxShadow: '0 0 0 2px rgba(59, 130, 246, 0.3)',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Sign In',
      signupText: 'Sign Up',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Email',
      resendText: 'Resend Email',
      height: '48px',
      width: '100%',
      borderRadius: '6px',
      hoverBackground: '#2563eb'
    },
    navLinks: {
      signinPrompt: 'Already have an account?',
      signinLinkText: 'Sign in',
      signupPrompt: "Don't have an account?",
      signupLinkText: 'Sign up',
      forgotPrompt: 'Forgot your password?',
      forgotLinkText: 'Reset it',
      fontSize: '0.875rem',
      color: '#d1d5db',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '400',
      linkColor: '#3b82f6',
      linkFontWeight: '500',
      backToSigninPrompt: 'Back to sign in'
    },
    passwordToggle: {
      position: 'right',
      rightOffset: '12px',
      color: '#9ca3af',
      hoverColor: '#ffffff',
      size: '14px',
      padding: '2px'
    }
  },
  preview: {
    primaryColor: '#3b82f6',
    backgroundColor: '#1f2937',
    borderRadius: '12px',
    fontFamily: 'System'
  }
};

export const classicPreset: PresetDesign = {
  name: 'classic',
  displayName: 'Classic',
  description: 'Traditional, professional design with clean lines and standard typography',
  category: 'traditional',
  customization: {
    layout: {
      padding: '2rem',
      paddingTop: '1.5rem',
      margin: '2rem',
      borderRadius: '4px',
      maxWidth: '400px',
      minWidth: '300px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#ffffff',
      buttonBackground: '#1f2937',
      buttonText: '#ffffff',
      inputBackground: '#ffffff',
      inputText: '#111827',
      inputPlaceholder: '#9ca3af',
      borderColor: '#d1d5db'
    },
    typography: {
      titleText: {
        signinText: 'Sign In',
        signupText: 'Sign Up',
        resetText: 'Reset Password',
        updateText: 'Update Password',
        verifyText: 'Verify Email',
        resendText: 'Resend Confirmation'
      },
      subtitleText: {
        signinText: '',
        signupText: '',
        resetText: '',
        updateText: '',
        verifyText: '',
        resendText: ''
      },
      titleSize: '2rem',
      titleColor: '#111827',
      labelSize: '0.875rem',
      fontFamily: 'Georgia, "Times New Roman", Times, serif',
      labelColor: '#374151',
      labelFontWeight: '400',
      titleAlignment: 'center',
      titleWeight: '600',
      titleLineHeight: '1.2',
      termsText: {
        agreePrefix: 'I agree with the',
        andConnector: 'and',
        defaultPrefix: 'default',
        linkText: {
          terms: 'Terms of Service',
          privacy: 'Privacy Policy',
          notifications: 'Notification Settings'
        },
        textColor: '#4b5563',
        linkColor: '#1f2937'
      },
      navTextColor: '#374151',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'Email Address',
      passwordPlaceholder: 'Password',
      usernamePlaceholder: 'Username',
      confirmPasswordPlaceholder: 'Confirm Password',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      usernameLabel: 'Username',
      confirmPasswordLabel: 'Confirm Password',
      borderRadius: '4px',
      height: '44px',
      width: '100%',
      padding: '0 0.75rem',
      margin: '0 0 1rem 0',
      fontSize: '1rem',
      fontWeight: '400',
      focusBorderColor: '#1f2937',
      focusBoxShadow: 'none',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Sign In',
      signupText: 'Sign Up',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Email',
      resendText: 'Resend Confirmation',
      height: '44px',
      width: '100%',
      borderRadius: '4px',
      hoverBackground: '#374151'
    },
    navLinks: {
      signinPrompt: 'Already have an account?',
      signinLinkText: 'Sign In',
      signupPrompt: "Don't have an account?",
      signupLinkText: 'Sign Up',
      forgotPrompt: 'Forgot Password?',
      forgotLinkText: 'Reset',
      fontSize: '0.875rem',
      color: '#4b5563',
      fontFamily: 'Georgia, "Times New Roman", Times, serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '400',
      linkColor: '#1f2937',
      linkFontWeight: '500',
      backToSigninPrompt: 'Back to Sign In'
    }
  },
  preview: {
    primaryColor: '#1f2937',
    backgroundColor: '#ffffff',
    borderRadius: '4px',
    fontFamily: 'Georgia'
  }
};

export const minimalPreset: PresetDesign = {
  name: 'minimal',
  displayName: 'Minimal',
  description: 'Ultra-clean design with maximum whitespace and minimal visual elements',
  category: 'modern',
  customization: {
    layout: {
      padding: '3rem',
      paddingTop: '2rem',
      margin: '1rem',
      borderRadius: '0px',
      maxWidth: '380px',
      minWidth: '300px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#ffffff',
      buttonBackground: '#000000',
      buttonText: '#ffffff',
      inputBackground: '#ffffff',
      inputText: '#000000',
      inputPlaceholder: '#a3a3a3',
      borderColor: '#e5e5e5'
    },
    typography: {
      titleText: {
        signinText: 'Sign In',
        signupText: 'Sign Up',
        resetText: 'Reset',
        updateText: 'Update',
        verifyText: 'Verify',
        resendText: 'Resend'
      },
      subtitleText: {
        signinText: '',
        signupText: '',
        resetText: '',
        updateText: '',
        verifyText: '',
        resendText: ''
      },
      titleSize: '1.5rem',
      titleColor: '#000000',
      labelSize: '0.75rem',
      fontFamily: 'Helvetica Neue, Helvetica, Arial, sans-serif',
      labelColor: '#666666',
      labelFontWeight: '300',
      titleAlignment: 'left',
      titleWeight: '300',
      titleLineHeight: '1.1',
      termsText: {
        agreePrefix: 'I agree to the',
        andConnector: '&',
        defaultPrefix: 'default',
        linkText: {
          terms: 'Terms',
          privacy: 'Privacy',
          notifications: 'Notifications'
        },
        textColor: '#999999',
        linkColor: '#000000'
      },
      navTextColor: '#666666',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'email',
      passwordPlaceholder: 'password',
      usernamePlaceholder: 'username',
      confirmPasswordPlaceholder: 'confirm password',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      usernameLabel: 'Username',
      confirmPasswordLabel: 'Confirm',
      borderRadius: '0px',
      height: '50px',
      width: '100%',
      padding: '0 0',
      margin: '0 0 2rem 0',
      fontSize: '1rem',
      fontWeight: '300',
      focusBorderColor: '#000000',
      focusBoxShadow: 'none',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Sign In',
      signupText: 'Sign Up',
      resetText: 'Reset',
      updateText: 'Update',
      verifyText: 'Verify',
      resendText: 'Resend',
      height: '50px',
      width: '100%',
      borderRadius: '0px',
      hoverBackground: '#333333'
    },
    navLinks: {
      signinPrompt: 'Have an account?',
      signinLinkText: 'Sign in',
      signupPrompt: 'Need an account?',
      signupLinkText: 'Sign up',
      forgotPrompt: 'Forgot password?',
      forgotLinkText: 'Reset',
      fontSize: '0.75rem',
      color: '#999999',
      fontFamily: 'Helvetica Neue, Helvetica, Arial, sans-serif',
      textAlign: 'left',
      marginTop: '2rem',
      marginBottom: '0',
      fontWeight: '300',
      linkColor: '#000000',
      linkFontWeight: '300',
      backToSigninPrompt: 'Back'
    }
  },
  preview: {
    primaryColor: '#000000',
    backgroundColor: '#ffffff',
    borderRadius: '0px',
    fontFamily: 'Helvetica Neue'
  }
};

export const corporatePreset: PresetDesign = {
  name: 'corporate',
  displayName: 'Corporate',
  description: 'Professional business design with corporate colors and formal typography',
  category: 'professional',
  customization: {
    layout: {
      padding: '2.5rem',
      paddingTop: '2rem',
      margin: '2rem',
      borderRadius: '8px',
      maxWidth: '450px',
      minWidth: '350px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#f8f9fa',
      buttonBackground: '#0d47a1',
      buttonText: '#ffffff',
      inputBackground: '#ffffff',
      inputText: '#212529',
      inputPlaceholder: '#6c757d',
      borderColor: '#ced4da'
    },
    typography: {
      titleText: {
        signinText: 'Employee Portal',
        signupText: 'Register Account',
        resetText: 'Password Recovery',
        updateText: 'Update Credentials',
        verifyText: 'Account Verification',
        resendText: 'Resend Verification'
      },
      subtitleText: {
        signinText: 'Access your corporate account',
        signupText: 'Create your employee account',
        resetText: 'Recover your account access',
        updateText: 'Update your login credentials',
        verifyText: 'Verify your email address',
        resendText: 'Request new verification email'
      },
      titleSize: '1.75rem',
      titleColor: '#0d47a1',
      labelSize: '0.875rem',
      fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
      labelColor: '#495057',
      labelFontWeight: '500',
      titleAlignment: 'center',
      titleWeight: '600',
      titleLineHeight: '1.3',
      termsText: {
        agreePrefix: 'I acknowledge and agree to the',
        andConnector: 'and',
        defaultPrefix: 'company',
        linkText: {
          terms: 'Terms of Service',
          privacy: 'Privacy Policy',
          notifications: 'Communication Preferences'
        },
        textColor: '#6c757d',
        linkColor: '#0d47a1'
      },
      navTextColor: '#495057',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'Corporate Email Address',
      passwordPlaceholder: 'Secure Password',
      usernamePlaceholder: 'Employee ID or Username',
      confirmPasswordPlaceholder: 'Confirm New Password',
      emailLabel: 'Email Address',
      passwordLabel: 'Password',
      usernameLabel: 'Employee ID',
      confirmPasswordLabel: 'Confirm Password',
      borderRadius: '6px',
      height: '46px',
      width: '100%',
      padding: '0 1rem',
      margin: '0 0 1.25rem 0',
      fontSize: '1rem',
      fontWeight: '400',
      focusBorderColor: '#0d47a1',
      focusBoxShadow: '0 0 0 2px rgba(13, 71, 161, 0.25)',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Access Portal',
      signupText: 'Register Account',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Account',
      resendText: 'Resend Verification',
      height: '46px',
      width: '100%',
      borderRadius: '6px',
      hoverBackground: '#1565c0'
    },
    navLinks: {
      signinPrompt: 'Already registered?',
      signinLinkText: 'Sign in here',
      signupPrompt: 'Need an account?',
      signupLinkText: 'Register now',
      forgotPrompt: 'Forgot your password?',
      forgotLinkText: 'Reset password',
      fontSize: '0.875rem',
      color: '#6c757d',
      fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '400',
      linkColor: '#0d47a1',
      linkFontWeight: '500',
      backToSigninPrompt: 'Return to sign in'
    }
  },
  preview: {
    primaryColor: '#0d47a1',
    backgroundColor: '#f8f9fa',
    borderRadius: '8px',
    fontFamily: 'Segoe UI'
  }
};
