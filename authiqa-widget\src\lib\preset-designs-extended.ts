import { PresetDesign } from './preset-types';

export const gradientPreset: PresetDesign = {
  name: 'gradient',
  displayName: 'Gradient',
  description: 'Modern gradient design with vibrant colors and smooth transitions',
  category: 'creative',
  customization: {
    layout: {
      padding: '2rem',
      paddingTop: '1.5rem',
      margin: '1rem',
      borderRadius: '16px',
      maxWidth: '400px',
      minWidth: '320px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      buttonBackground: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      buttonText: '#ffffff',
      inputBackground: 'rgba(255, 255, 255, 0.9)',
      inputText: '#2d3748',
      inputPlaceholder: '#718096',
      borderColor: 'rgba(255, 255, 255, 0.3)'
    },
    typography: {
      titleText: {
        signinText: 'Welcome Back',
        signupText: 'Join Us',
        resetText: 'Reset Password',
        updateText: 'Update Password',
        verifyText: 'Verify Email',
        resendText: 'Resend Email'
      },
      subtitleText: {
        signinText: 'Sign in to continue your journey',
        signupText: 'Start your amazing journey',
        resetText: 'We\'ll help you reset it',
        updateText: 'Choose a new secure password',
        verifyText: 'Check your email for verification',
        resendText: 'Get a new verification email'
      },
      titleSize: '2rem',
      titleColor: '#ffffff',
      labelSize: '0.875rem',
      fontFamily: '"Poppins", -apple-system, BlinkMacSystemFont, sans-serif',
      labelColor: '#ffffff',
      labelFontWeight: '500',
      titleAlignment: 'center',
      titleWeight: '600',
      titleLineHeight: '1.2',
      termsText: {
        agreePrefix: 'I agree to the',
        andConnector: 'and',
        defaultPrefix: 'default',
        linkText: {
          terms: 'Terms of Service',
          privacy: 'Privacy Policy',
          notifications: 'Notification Settings'
        },
        textColor: 'rgba(255, 255, 255, 0.8)',
        linkColor: '#ffffff'
      },
      navTextColor: 'rgba(255, 255, 255, 0.9)',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'Your email address',
      passwordPlaceholder: 'Your password',
      usernamePlaceholder: 'Choose username',
      confirmPasswordPlaceholder: 'Confirm password',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      usernameLabel: 'Username',
      confirmPasswordLabel: 'Confirm Password',
      borderRadius: '12px',
      height: '50px',
      width: '100%',
      padding: '0 1rem',
      margin: '0 0 1rem 0',
      fontSize: '1rem',
      fontWeight: '400',
      focusBorderColor: '#ffffff',
      focusBoxShadow: '0 0 0 3px rgba(255, 255, 255, 0.3)',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Sign In',
      signupText: 'Get Started',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Email',
      resendText: 'Resend Email',
      height: '50px',
      width: '100%',
      borderRadius: '12px',
      hoverBackground: 'linear-gradient(135deg, #f5576c 0%, #f093fb 100%)'
    },
    navLinks: {
      signinPrompt: 'Already have an account?',
      signinLinkText: 'Sign in',
      signupPrompt: 'New here?',
      signupLinkText: 'Create account',
      forgotPrompt: 'Forgot password?',
      forgotLinkText: 'Reset it',
      fontSize: '0.875rem',
      color: 'rgba(255, 255, 255, 0.9)',
      fontFamily: '"Poppins", -apple-system, BlinkMacSystemFont, sans-serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '400',
      linkColor: '#ffffff',
      linkFontWeight: '600',
      backToSigninPrompt: 'Back to sign in'
    }
  },
  preview: {
    primaryColor: '#f093fb',
    backgroundColor: '#667eea',
    borderRadius: '16px',
    fontFamily: 'Poppins'
  }
};

export const roundedPreset: PresetDesign = {
  name: 'rounded',
  displayName: 'Rounded',
  description: 'Friendly design with rounded corners and soft, approachable styling',
  category: 'modern',
  customization: {
    layout: {
      padding: '2rem',
      paddingTop: '1.5rem',
      margin: '1.5rem',
      borderRadius: '24px',
      maxWidth: '400px',
      minWidth: '320px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#ffffff',
      buttonBackground: '#10b981',
      buttonText: '#ffffff',
      inputBackground: '#f9fafb',
      inputText: '#111827',
      inputPlaceholder: '#9ca3af',
      borderColor: '#e5e7eb'
    },
    typography: {
      titleText: {
        signinText: 'Hello Again!',
        signupText: 'Welcome!',
        resetText: 'Reset Password',
        updateText: 'Update Password',
        verifyText: 'Verify Email',
        resendText: 'Resend Email'
      },
      subtitleText: {
        signinText: 'We\'re happy to see you back',
        signupText: 'Let\'s get you started',
        resetText: 'Don\'t worry, it happens to everyone',
        updateText: 'Choose a strong new password',
        verifyText: 'Please check your email',
        resendText: 'We\'ll send another email'
      },
      titleSize: '1.875rem',
      titleColor: '#1f2937',
      labelSize: '0.875rem',
      fontFamily: '"Nunito", -apple-system, BlinkMacSystemFont, sans-serif',
      labelColor: '#374151',
      labelFontWeight: '600',
      titleAlignment: 'center',
      titleWeight: '700',
      titleLineHeight: '1.25',
      termsText: {
        agreePrefix: 'I agree to the',
        andConnector: 'and',
        defaultPrefix: 'default',
        linkText: {
          terms: 'Terms of Service',
          privacy: 'Privacy Policy',
          notifications: 'Notification Settings'
        },
        textColor: '#6b7280',
        linkColor: '#10b981'
      },
      navTextColor: '#6b7280',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'Enter your email',
      passwordPlaceholder: 'Enter your password',
      usernamePlaceholder: 'Choose a username',
      confirmPasswordPlaceholder: 'Confirm your password',
      emailLabel: 'Email Address',
      passwordLabel: 'Password',
      usernameLabel: 'Username',
      confirmPasswordLabel: 'Confirm Password',
      borderRadius: '16px',
      height: '52px',
      width: '100%',
      padding: '0 1.25rem',
      margin: '0 0 1rem 0',
      fontSize: '1rem',
      fontWeight: '400',
      focusBorderColor: '#10b981',
      focusBoxShadow: '0 0 0 3px rgba(16, 185, 129, 0.1)',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'Sign In',
      signupText: 'Get Started',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Email',
      resendText: 'Resend Email',
      height: '52px',
      width: '100%',
      borderRadius: '16px',
      hoverBackground: '#059669'
    },
    navLinks: {
      signinPrompt: 'Already have an account?',
      signinLinkText: 'Sign in here',
      signupPrompt: 'New to our platform?',
      signupLinkText: 'Create account',
      forgotPrompt: 'Forgot your password?',
      forgotLinkText: 'Reset it here',
      fontSize: '0.875rem',
      color: '#6b7280',
      fontFamily: '"Nunito", -apple-system, BlinkMacSystemFont, sans-serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '500',
      linkColor: '#10b981',
      linkFontWeight: '600',
      backToSigninPrompt: 'Back to sign in'
    }
  },
  preview: {
    primaryColor: '#10b981',
    backgroundColor: '#ffffff',
    borderRadius: '24px',
    fontFamily: 'Nunito'
  }
};

export const sharpPreset: PresetDesign = {
  name: 'sharp',
  displayName: 'Sharp',
  description: 'Bold, angular design with sharp edges and high contrast',
  category: 'modern',
  customization: {
    layout: {
      padding: '2rem',
      paddingTop: '1.5rem',
      margin: '1rem',
      borderRadius: '0px',
      maxWidth: '400px',
      minWidth: '320px',
      width: 'auto',
      height: 'auto',
      minHeight: 'auto',
      maxHeight: 'auto'
    },
    colors: {
      background: '#ffffff',
      buttonBackground: '#dc2626',
      buttonText: '#ffffff',
      inputBackground: '#ffffff',
      inputText: '#111827',
      inputPlaceholder: '#6b7280',
      borderColor: '#374151'
    },
    typography: {
      titleText: {
        signinText: 'ACCESS',
        signupText: 'REGISTER',
        resetText: 'RESET',
        updateText: 'UPDATE',
        verifyText: 'VERIFY',
        resendText: 'RESEND'
      },
      subtitleText: {
        signinText: '',
        signupText: '',
        resetText: '',
        updateText: '',
        verifyText: '',
        resendText: ''
      },
      titleSize: '2.25rem',
      titleColor: '#111827',
      labelSize: '0.75rem',
      fontFamily: '"Roboto Condensed", "Arial Narrow", Arial, sans-serif',
      labelColor: '#374151',
      labelFontWeight: '700',
      titleAlignment: 'center',
      titleWeight: '900',
      titleLineHeight: '1',
      termsText: {
        agreePrefix: 'I ACCEPT THE',
        andConnector: '&',
        defaultPrefix: 'STANDARD',
        linkText: {
          terms: 'TERMS',
          privacy: 'PRIVACY',
          notifications: 'NOTIFICATIONS'
        },
        textColor: '#6b7280',
        linkColor: '#dc2626'
      },
      navTextColor: '#6b7280',
      navTextColorDark: '#ffffff'
    },
    inputs: {
      emailPlaceholder: 'EMAIL',
      passwordPlaceholder: 'PASSWORD',
      usernamePlaceholder: 'USERNAME',
      confirmPasswordPlaceholder: 'CONFIRM',
      emailLabel: 'EMAIL',
      passwordLabel: 'PASSWORD',
      usernameLabel: 'USERNAME',
      confirmPasswordLabel: 'CONFIRM',
      borderRadius: '0px',
      height: '48px',
      width: '100%',
      padding: '0 1rem',
      margin: '0 0 1rem 0',
      fontSize: '1rem',
      fontWeight: '500',
      focusBorderColor: '#dc2626',
      focusBoxShadow: 'none',
      placeholderAlign: 'left'
    },
    buttons: {
      signinText: 'ACCESS',
      signupText: 'REGISTER',
      resetText: 'RESET',
      updateText: 'UPDATE',
      verifyText: 'VERIFY',
      resendText: 'RESEND',
      height: '48px',
      width: '100%',
      borderRadius: '0px',
      hoverBackground: '#b91c1c'
    },
    navLinks: {
      signinPrompt: 'HAVE ACCOUNT?',
      signinLinkText: 'ACCESS',
      signupPrompt: 'NEED ACCOUNT?',
      signupLinkText: 'REGISTER',
      forgotPrompt: 'FORGOT?',
      forgotLinkText: 'RESET',
      fontSize: '0.75rem',
      color: '#6b7280',
      fontFamily: '"Roboto Condensed", "Arial Narrow", Arial, sans-serif',
      textAlign: 'center',
      marginTop: '1.5rem',
      marginBottom: '0',
      fontWeight: '700',
      linkColor: '#dc2626',
      linkFontWeight: '700',
      backToSigninPrompt: 'BACK'
    }
  },
  preview: {
    primaryColor: '#dc2626',
    backgroundColor: '#ffffff',
    borderRadius: '0px',
    fontFamily: 'Roboto Condensed'
  }
};
