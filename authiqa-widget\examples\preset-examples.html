<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authiqa Widget Preset Examples</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .preset-demo {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .preset-info {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .preset-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 5px 0;
            color: #333;
        }
        .preset-description {
            color: #666;
            font-size: 0.9rem;
            margin: 0;
        }
        .preset-category {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-top: 8px;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        .preset-selector {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            margin: 0 10px;
        }
        .apply-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
        }
        .apply-btn:hover {
            background: #1d4ed8;
        }
        .widget-container {
            min-height: 400px;
        }
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Authiqa Widget Preset Examples</h1>
            <p>Explore different preset designs for the Authiqa authentication widget</p>
        </div>

        <div class="controls">
            <select id="presetSelector" class="preset-selector">
                <option value="modern">Modern</option>
                <option value="classic">Classic</option>
                <option value="minimal">Minimal</option>
                <option value="corporate">Corporate</option>
                <option value="gradient">Gradient</option>
                <option value="rounded">Rounded</option>
            </select>
            <button id="applyPreset" class="apply-btn">Apply Preset</button>
        </div>

        <div class="preset-grid">
            <!-- Modern Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Modern</h3>
                    <p class="preset-description">Clean, contemporary design with subtle shadows and modern typography</p>
                    <span class="preset-category">Modern</span>
                </div>
                <div id="modern-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'modern-widget',
  mode: 'inline',
  preset: 'modern',
  organizationDomain: 'example.com'
});
                </div>
            </div>

            <!-- Classic Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Classic</h3>
                    <p class="preset-description">Facebook-inspired clean design with blue primary button and green secondary button</p>
                    <span class="preset-category">Traditional</span>
                </div>
                <div id="classic-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'classic-widget',
  mode: 'inline',
  preset: 'classic',
  organizationDomain: 'example.com'
});
                </div>
            </div>

            <!-- Minimal Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Minimal</h3>
                    <p class="preset-description">Ultra-clean design with maximum whitespace and minimal visual elements</p>
                    <span class="preset-category">Modern</span>
                </div>
                <div id="minimal-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'minimal-widget',
  mode: 'inline',
  preset: 'minimal',
  organizationDomain: 'example.com'
});
                </div>
            </div>

            <!-- Corporate Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Corporate</h3>
                    <p class="preset-description">Professional business design with corporate colors and formal typography</p>
                    <span class="preset-category">Professional</span>
                </div>
                <div id="corporate-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'corporate-widget',
  mode: 'inline',
  preset: 'corporate',
  organizationDomain: 'example.com'
});
                </div>
            </div>

            <!-- Gradient Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Gradient</h3>
                    <p class="preset-description">Modern gradient design with vibrant colors and smooth transitions</p>
                    <span class="preset-category">Creative</span>
                </div>
                <div id="gradient-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'gradient-widget',
  mode: 'inline',
  preset: 'gradient',
  organizationDomain: 'example.com'
});
                </div>
            </div>

            <!-- Rounded Preset -->
            <div class="preset-demo">
                <div class="preset-info">
                    <h3 class="preset-title">Rounded</h3>
                    <p class="preset-description">Friendly design with rounded corners and soft, approachable styling</p>
                    <span class="preset-category">Modern</span>
                </div>
                <div id="rounded-widget" class="widget-container"></div>
                <div class="code-example">
const widget = new AuthiqaWidget({
  publicKey: 'your-key',
  container: 'rounded-widget',
  mode: 'inline',
  preset: 'rounded',
  organizationDomain: 'example.com'
});
                </div>
            </div>
        </div>
    </div>

    <!-- Include the Authiqa Widget -->
    <script src="../dist/authiqa-widget.js"></script>
    <script>
        // Initialize widgets with different presets
        const widgets = {};
        const presets = ['modern', 'classic', 'minimal', 'corporate', 'gradient', 'rounded'];
        
        presets.forEach(preset => {
            widgets[preset] = new AuthiqaWidget({
                publicKey: 'demo-key',
                container: `${preset}-widget`,
                mode: 'inline',
                preset: preset,
                organizationDomain: 'demo.authiqa.com'
            });
            
            // Show signin form for each widget
            widgets[preset].show('signin');
        });

        // Dynamic preset switching
        document.getElementById('applyPreset').addEventListener('click', () => {
            const selectedPreset = document.getElementById('presetSelector').value;
            
            // Apply the selected preset to the modern widget as an example
            widgets.modern.applyPreset(selectedPreset);
        });
    </script>
</body>
</html>
