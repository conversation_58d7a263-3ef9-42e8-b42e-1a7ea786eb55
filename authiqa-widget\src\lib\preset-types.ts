import { WidgetCustomization } from './customization-types';

export type PresetName =
  | 'modern'
  | 'classic'
  | 'minimal'
  | 'corporate'
  | 'gradient'
  | 'rounded'
  | 'sharp';

export interface PresetDesign {
  name: PresetName;
  displayName: string;
  description: string;
  category: 'modern' | 'traditional' | 'creative' | 'professional';
  customization: WidgetCustomization;
  preview?: {
    primaryColor: string;
    backgroundColor: string;
    borderRadius: string;
    fontFamily: string;
  };
}

export interface PresetConfig {
  preset?: PresetName;
  customOverrides?: Partial<WidgetCustomization>;
}

export interface PresetManager {
  getPreset(name: PresetName): PresetDesign | null;
  getAllPresets(): PresetDesign[];
  getPresetsByCategory(category: string): PresetDesign[];
  mergePresetWithCustomization(preset: PresetDesign, customization?: Partial<WidgetCustomization>): WidgetCustomization;
  applyPreset(name: PresetName, customOverrides?: Partial<WidgetCustomization>): WidgetCustomization;
}
