# Authiqa Widget Preset Designs

The Authiqa widget now supports preset designs that provide pre-configured styling options for quick implementation. This document outlines all available presets and how to use them.

## Available Presets

### 1. Modern (`modern`)
**Category:** Modern  
**Description:** Clean, contemporary design with subtle shadows and modern typography

**Key Features:**
- Inter font family
- Blue accent color (#2563eb)
- Rounded corners (12px)
- Focus states with subtle shadows
- Clean, spacious layout

**Usage:**
```javascript
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern',
  organizationDomain: 'your-domain.com'
});
```

### 2. Classic (`classic`)
**Category:** Traditional  
**Description:** Traditional, professional design with clean lines and standard typography

**Key Features:**
- Georgia serif font
- Dark gray colors (#1f2937)
- Minimal border radius (4px)
- Professional, conservative styling
- Standard form layouts

### 3. Minimal (`minimal`)
**Category:** Modern  
**Description:** Ultra-clean design with maximum whitespace and minimal visual elements

**Key Features:**
- Helvetica Neue font
- Black and white color scheme
- No border radius (sharp corners)
- Maximum whitespace
- Left-aligned titles
- Minimal visual elements

### 4. Corporate (`corporate`)
**Category:** Professional  
**Description:** Professional business design with corporate colors and formal typography

**Key Features:**
- Segoe UI font family
- Corporate blue (#0d47a1)
- Light gray background (#f8f9fa)
- Formal language and terminology
- Professional spacing and sizing

### 5. Gradient (`gradient`)
**Category:** Creative  
**Description:** Modern gradient design with vibrant colors and smooth transitions

**Key Features:**
- Poppins font family
- Gradient backgrounds
- Vibrant color scheme
- Semi-transparent elements
- Creative, eye-catching design

### 6. Rounded (`rounded`)
**Category:** Modern
**Description:** Friendly design with rounded corners and soft, approachable styling

**Key Features:**
- Nunito font family
- Green accent color (#10b981)
- Heavy border radius (24px for container, 16px for inputs)
- Friendly, approachable language
- Soft, welcoming design

### 7. Sharp (`sharp`)
**Category:** Modern
**Description:** Bold, angular design with sharp edges and high contrast

**Key Features:**
- Roboto Condensed font family
- Red accent color (#dc2626)
- No border radius (sharp corners)
- Bold, uppercase text
- High contrast design
- Angular, aggressive styling

## Usage Examples

### Basic Preset Usage
```javascript
// Using a preset without customization
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern',
  organizationDomain: 'your-domain.com'
});
```

### Preset with Custom Overrides
```javascript
// Using a preset with custom color overrides
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern',
  customization: {
    colors: {
      buttonBackground: '#ff6b6b', // Override button color
      buttonText: '#ffffff'
    },
    typography: {
      titleText: {
        signinText: 'Welcome to Our Platform' // Custom title
      }
    }
  },
  organizationDomain: 'your-domain.com'
});
```

### Dynamic Preset Switching
```javascript
// Initialize widget
const widget = new AuthiqaWidget({
  publicKey: 'your-public-key',
  container: 'authiqa',
  mode: 'inline',
  preset: 'modern',
  organizationDomain: 'your-domain.com'
});

// Later, switch to a different preset
widget.applyPreset('minimal');

// Or switch with custom overrides
widget.applyPreset('corporate', {
  colors: {
    buttonBackground: '#your-brand-color'
  }
});
```

### Getting Available Presets
```javascript
// Get list of all available presets
const presets = widget.getAvailablePresets();
console.log(presets);
// Output: [
//   { name: 'modern', displayName: 'Modern', description: '...', category: 'modern' },
//   { name: 'classic', displayName: 'Classic', description: '...', category: 'traditional' },
//   ...
// ]

// Get current preset
const currentPreset = widget.getCurrentPreset();
console.log(currentPreset); // 'modern'
```

## Preset Categories

- **Modern**: Contemporary designs with current design trends
- **Traditional**: Classic, conservative designs
- **Creative**: Bold, artistic designs with unique elements
- **Professional**: Business-focused, formal designs

## Customization Priority

When using presets with custom overrides, the merge priority is:
1. Preset base configuration (lowest priority)
2. Custom overrides (highest priority)

This means any customization you provide will override the preset's default values for those specific properties.

## Best Practices

1. **Choose the Right Preset**: Select a preset that matches your brand personality and target audience
2. **Minimal Overrides**: Use presets as-is when possible, only override specific elements that need to match your brand
3. **Consistent Branding**: When overriding colors, ensure they maintain good contrast and accessibility
4. **Test Responsiveness**: All presets are designed to be responsive, but test your overrides across different screen sizes

## Browser Support

All presets are designed to work across modern browsers and include fallbacks for older browsers where necessary. The preset system uses CSS-in-JS generation for maximum compatibility.
