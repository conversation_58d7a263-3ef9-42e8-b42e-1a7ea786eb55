!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Authiqa=e():t.Authiqa=e()}(this,(()=>(()=>{"use strict";var t={959:(t,e,n)=>{n.d(e,{A:()=>s});var o=n(601),i=n.n(o),a=n(314),r=n.n(a)()(i());r.push([t.id,'/* Base styles */\nbody {\n    margin: 0;\n    min-height: 100vh;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\nbody[data-theme="dark"] {\n    background-color: #18181b;\n}\n\n.authiqa-container {\n    font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, Oxygen, Ubuntu, Cantarell, \'Open Sans\', \'Helvetica Neue\', sans-serif;\n    max-width: 400px;\n    width: 100%;\n    margin: 2rem;\n    padding: 1.25rem 1.5rem 1.5rem 1.5rem; /* Reduced horizontal padding from 2.5rem to 1.5rem */\n    border-radius: 16px;\n}\n\n.authiqa-container h1 {\n    font-size: 2rem;\n    font-weight: 600;\n    margin-top: 0; /* Added to ensure no extra top margin */\n    margin-bottom: 2rem;\n    color: #1a1a1a;\n    text-align: center;\n}\n\n.authiqa-container form {\n    display: flex;\n    flex-direction: column;\n    gap: 1.5rem;\n}\n\n.authiqa-container label,\n.authiqa-label {\n    font-size: 1rem;\n    font-weight: 400;\n    color: #1a1a1a;\n    margin-bottom: 0.5rem !important; /* Changed to 0.3rem (approximately 5px) */\n    padding-left: 0.09rem !important; /* Added left padding to move labels slightly right */\n    display: block;\n    height: 14px !important; /* Added fixed height */\n    line-height: 14px !important; /* Added line height to match height */\n}\n\n/* Add more spacing between input groups */\n.authiqa-container .authiqa-labeled-input {\n    margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1rem (about 16px) */\n}\n\n.authiqa-container input[type="text"],\n.authiqa-container input[type="email"],\n.authiqa-container input[type="password"] {\n    width: 100%;\n    height: 50px; /* Set height to 64px (14x64) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    border: 1px solid #e5e5e5;\n    border-radius: 4px;\n    background-color: #ffffff;\n    transition: border-color 0.2s ease;\n    box-sizing: border-box;\n}\n\n.authiqa-container input[type="text"]:focus,\n.authiqa-container input[type="email"]:focus,\n.authiqa-container input[type="password"]:focus {\n    outline: none;\n    border-color: #000000;\n}\n\n.authiqa-container input[type="text"]::placeholder,\n.authiqa-container input[type="email"]::placeholder,\n.authiqa-container input[type="password"]::placeholder {\n    color: #a3a3a3;\n}\n\n.authiqa-container .terms-container {\n    display: flex;\n    align-items: flex-start;\n    gap: 0.75rem;\n    margin-bottom: 1rem;\n}\n\n.authiqa-container input[type="checkbox"] {\n    margin-top: 0.25rem; /* Adjusted to align with text */\n    margin-right: 0.5rem; /* Standardized margin */\n    margin-left: 0; /* Reset left margin */\n    position: static; /* Remove relative positioning */\n    top: auto; /* Remove top offset */\n}\n\n.authiqa-container .terms-container label {\n    font-size: 0.875rem;\n    color: #525252;\n    line-height: 1.4;\n    margin-top: 0; /* Ensure no top margin */\n    padding-top: 0; /* Ensure no top padding */\n}\n\n.authiqa-container .terms-container a {\n    color: #000000;\n    text-decoration: none;\n}\n\n.authiqa-container .terms-container a:hover {\n    text-decoration: underline;\n}\n\n.authiqa-container .forgot-password,\n.authiqa-container .alternate-action {\n    color: #ffffff !important;\n    font-size: 0.95rem !important;\n    text-align: left !important;\n}\n.authiqa-container .alternate-action {\n    text-align: center;\n}\n.authiqa-container .forgot-password a,\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n.authiqa-container .forgot-password a:hover,\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Update the button styles to include proper centering */\n.authiqa-container button[type="submit"] {\n    width: 100%;\n    height: 40px; /* Changed to fixed 40px height (approximately 14px) */\n    padding: 0 1rem;\n    font-size: 1rem;\n    font-weight: 500;\n    color: #ffffff;\n    background-color: #18181b;\n    border: none;\n    border-radius: 4px;\n    cursor: pointer;\n    transition: all 0.2s ease;\n    position: relative;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    line-height: 1;\n    margin-top: 0.5rem; /* Decreased from 1rem to 0.5rem (about 8px) */\n}\n\n.authiqa-container button[type="submit"]:hover {\n    background-color: #27272a;\n    transform: translateY(-1px);\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n/* Add this new style for the active/clicked state */\n.authiqa-container button[type="submit"]:active {\n    transform: scale(0.98);\n    box-shadow: none;\n    background-color: #000000;\n}\n\n.authiqa-container button[type="submit"]:disabled {\n    background-color: #71717a;\n    cursor: not-allowed;\n    transform: none;\n    box-shadow: none;\n}\n\n/* Loading state styles */\n.authiqa-container button[type="submit"].loading {\n    position: relative;\n    color: transparent !important;\n}\n\n.authiqa-container button[type="submit"].loading::after {\n    content: \'\';\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    top: 50%;\n    left: 50%;\n    margin: -10px 0 0 -10px;\n    border: 2px solid #ffffff;\n    border-radius: 50%;\n    border-left-color: transparent;\n    animation: button-loading-spinner 1s linear infinite;\n}\n\n/* Loading text container */\n.authiqa-container .loading-text {\n    text-align: center;\n    margin-top: 8px;\n    font-size: 0.875rem;\n    color: #525252;\n}\n\n/* Dark theme loading text */\n.authiqa-container[data-theme="dark"] .loading-text {\n    color: #a1a1aa;\n}\n\n@keyframes button-loading-spinner {\n    0% {\n        transform: rotate(0deg);\n    }\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* Dark theme button styles */\n.authiqa-container[data-theme="dark"] button[type="submit"] {\n    background-color: #ffffff;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:hover {\n    background-color: #e5e5e5;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"]:disabled {\n    background-color: #a1a1aa;\n    color: #18181b;\n}\n\n.authiqa-container[data-theme="dark"] button[type="submit"].loading::after {\n    border-color: #18181b;\n    border-left-color: transparent;\n}\n\n/* Center alternate-action and set color for both themes */\n.authiqa-container .alternate-action {\n    text-align: center !important;\n    margin-top: 1.5rem;\n    font-size: 0.95rem;\n    color: #1a1a1a !important; /* Force dark text in light theme */\n}\n\n.authiqa-container .alternate-action a {\n    color: #10D5C6 !important;\n    text-decoration: underline;\n    font-weight: 500;\n    margin-left: 0.25rem;\n    transition: color 0.2s;\n}\n\n.authiqa-container .alternate-action a:hover {\n    color: #0ea5e9 !important;\n}\n\n/* Dark theme overrides */\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #ffffff !important;\n}\n\n/* Dark theme */\n.authiqa-container[data-theme="dark"] {\n    color: #ffffff;\n    background-color: #27272a;\n}\n\n.authiqa-container[data-theme="dark"] h1 {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] label,\n.authiqa-container[data-theme="dark"] .authiqa-label {\n    color: #ffffff !important; /* Ensure white label text in dark theme */\n    \n}\n\n.authiqa-container[data-theme="dark"] input[type="text"],\n.authiqa-container[data-theme="dark"] input[type="email"],\n.authiqa-container[data-theme="dark"] input[type="password"] {\n    background-color: #18181b;\n    border-color: #3f3f46;\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]:focus,\n.authiqa-container[data-theme="dark"] input[type="email"]:focus,\n.authiqa-container[data-theme="dark"] input[type="password"]:focus {\n    border-color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] input[type="text"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="email"]::placeholder,\n.authiqa-container[data-theme="dark"] input[type="password"]::placeholder {\n    color: #71717a;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container label {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .terms-container a,\n.authiqa-container[data-theme="dark"] .forgot-password a {\n    color: #ffffff;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .alternate-action a {\n    color: #ffffff;\n}\n\n.authiqa-container .password-field-container {\n    position: relative;\n    width: 100%;\n}\n\n.authiqa-container .password-toggle {\n    position: absolute;\n    right: 12px;\n    top: 50%;\n    transform: translateY(-50%);\n    border: none;\n    background: none;\n    cursor: pointer;\n    padding: 8px;\n    color: #71717a;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: color 0.2s ease;\n}\n\n.authiqa-container .password-toggle:hover {\n    color: #000000;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .password-toggle:hover {\n    color: #ffffff;\n}\n\n/* Message styles */\n.authiqa-message {\n    position: fixed;\n    top: 20px;\n    left: 50%;\n    transform: translateX(-50%);\n    z-index: 1000;\n    padding: 1rem 2rem;\n    border-radius: 0.375rem;\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n    animation: slideDown 0.3s ease-out;\n    max-width: 90%;\n    width: auto;\n    opacity: 0;\n    pointer-events: none;\n    transition: opacity 0.3s;\n}\n\n.authiqa-message.show {\n    opacity: 1;\n    pointer-events: auto;\n}\n\n.authiqa-message.success {\n    background-color: #dcfce7;\n    color: #15803d;\n    border-left: 4px solid #22c55e;\n}\n\n.authiqa-message.error {\n    background-color: #fee2e2;\n    color: #b91c1c;\n    border-left: 4px solid #ef4444;\n}\n\n@keyframes slideDown {\n    from {\n        transform: translate(-50%, -100%);\n        opacity: 0;\n    }\n    to {\n        transform: translate(-50%, 0);\n        opacity: 1;\n    }\n}\n\n/* Dark theme message styles */\nbody[data-theme="dark"] .authiqa-message.success {\n    background-color: #064e3b;\n    color: #ffffff;\n    border-left-color: #059669;\n}\n\nbody[data-theme="dark"] .authiqa-message.error {\n    background-color: #7f1d1d;\n    color: #ffffff;\n    border-left-color: #dc2626;\n}\n\n/* Password validation styling */\n.authiqa-container .password-validation-container {\n    display: grid;\n    grid-template-columns: 1fr 1fr; /* Two columns instead of flex-wrap */\n    gap: 0.5rem;\n    margin-top: 0.5rem;\n}\n\n.authiqa-container .validation-item {\n    display: flex;\n    align-items: center;\n    font-size: 0.75rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-dot {\n    margin-right: 0.25rem;\n    color: #a1a1aa;\n}\n\n.authiqa-container .validation-item.valid .validation-dot,\n.authiqa-container .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Dark theme adjustments */\n.authiqa-container[data-theme="dark"] .validation-item {\n    color: #a1a1aa;\n}\n\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-dot,\n.authiqa-container[data-theme="dark"] .validation-item.valid .validation-text {\n    color: #10D5C6;\n}\n\n/* Google button container - minimal styling to maintain layout */\n#google-button-container, #google-signup-button-container {\n    width: 100%;\n}\n',""]);const s=r},314:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",o=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),o&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),o&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,o,i,a){"string"==typeof t&&(t=[[null,t,void 0]]);var r={};if(o)for(var s=0;s<this.length;s++){var c=this[s][0];null!=c&&(r[c]=!0)}for(var l=0;l<t.length;l++){var d=[].concat(t[l]);o&&r[d[0]]||(void 0!==a&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=a),n&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=n):d[2]=n),i&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=i):d[4]="".concat(i)),e.push(d))}},e}},601:t=>{t.exports=function(t){return t[1]}},790:(t,e,n)=>{n.r(e),n.d(e,{default:()=>v});var o=n(72),i=n.n(o),a=n(825),r=n.n(a),s=n(659),c=n.n(s),l=n(56),d=n.n(l),u=n(540),h=n.n(u),p=n(494),m=n.n(p),g=n(959),f={};f.styleTagTransform=m(),f.setAttributes=d(),f.insert=c().bind(null,"head"),f.domAPI=r(),f.insertStyleElement=h(),i()(g.A,f);const v=g.A&&g.A.locals?g.A.locals:void 0},72:t=>{var e=[];function n(t){for(var n=-1,o=0;o<e.length;o++)if(e[o].identifier===t){n=o;break}return n}function o(t,o){for(var a={},r=[],s=0;s<t.length;s++){var c=t[s],l=o.base?c[0]+o.base:c[0],d=a[l]||0,u="".concat(l," ").concat(d);a[l]=d+1;var h=n(u),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==h)e[h].references++,e[h].updater(p);else{var m=i(p,o);o.byIndex=s,e.splice(s,0,{identifier:u,updater:m,references:1})}r.push(u)}return r}function i(t,e){var n=e.domAPI(e);return n.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,i){var a=o(t=t||[],i=i||{});return function(t){t=t||[];for(var r=0;r<a.length;r++){var s=n(a[r]);e[s].references--}for(var c=o(t,i),l=0;l<a.length;l++){var d=n(a[l]);0===e[d].references&&(e[d].updater(),e.splice(d,1))}a=c}}},659:t=>{var e={};t.exports=function(t,n){var o=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(n)}},540:t=>{t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},56:(t,e,n)=>{t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},825:t=>{t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var o="";n.supports&&(o+="@supports (".concat(n.supports,") {")),n.media&&(o+="@media ".concat(n.media," {"));var i=void 0!==n.layer;i&&(o+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),o+=n.css,i&&(o+="}"),n.media&&(o+="}"),n.supports&&(o+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),e.styleTagTransform(o,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},494:t=>{t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},156:function(t,e,n){var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)},i=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,a){function r(t){try{c(o.next(t))}catch(t){a(t)}}function s(t){try{c(o.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((o=o.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,o,i,a,r={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,o&&(i=2&s[0]?o.return:s[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,s[1])).done)return i;switch(o=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,o=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((i=(i=r.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(6===s[0]&&r.label<i[1]){r.label=i[1],i=s;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(s);break}i[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],o=0}finally{n=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.AuthiqaWidget=void 0;var r=n(395),s=n(752),c=n(92),l=n(113);n(790);var d=function(){function t(t){if(this.authUrls=null,this.currentAction=null,this.emailVerificationRequired=!1,console.log("🏗️ Widget constructor called with config:",{preset:t.preset}),window.AuthiqaGlobalConfig&&(window.AuthiqaGlobalConfig.customization&&(t.customization=o(o({},window.AuthiqaGlobalConfig.customization),t.customization)),window.AuthiqaGlobalConfig.messages&&(t.messages=o(o({},window.AuthiqaGlobalConfig.messages),t.messages))),t.preset){console.log("🎨 Applying preset in widget initialization:",t.preset);try{var e=r.presetManager.applyPreset(t.preset,t.customization);t.customization=e,console.log("✅ Preset applied successfully in initialization")}catch(e){console.warn('Failed to apply preset "'.concat(t.preset,'":'),e)}}else console.log("❌ No preset specified in config");this.config=t,this.api=new s.ApiService(t),this.injectStyles()}return t.prototype.storeTokens=function(t){"string"==typeof t?(localStorage.setItem("access_token",t),sessionStorage.setItem("refresh_token",t)):t.accessToken&&t.refreshToken&&(localStorage.setItem("access_token",t.accessToken),sessionStorage.setItem("refresh_token",t.refreshToken))},t.prototype.isUserAuthenticated=function(){var t=localStorage.getItem("access_token"),e=sessionStorage.getItem("refresh_token");return!(!t&&!e)},t.prototype.getStoredToken=function(){return sessionStorage.getItem("refresh_token")||localStorage.getItem("access_token")},t.prototype.getAuthUrls=function(){if(!this.authUrls)throw new Error("Widget not initialized. Call initialize() first.");return this.authUrls},t.prototype.applyPreset=function(t,e){try{var n=r.presetManager.applyPreset(t,e);this.config.preset=t,this.config.customization=n,this.injectStyles()}catch(e){throw console.error('Failed to apply preset "'.concat(t,'":'),e),e}},t.prototype.getAvailablePresets=function(){return r.presetManager.getAllPresets().map((function(t){return{name:t.name,displayName:t.displayName,description:t.description,category:t.category}}))},t.prototype.getCurrentPreset=function(){return this.config.preset},t.prototype.initialize=function(){var t,e;return i(this,void 0,void 0,(function(){var n,o,i;return a(this,(function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.api.getOrganizationDetails()];case 1:return n=a.sent(),this.authUrls=n.authUrls,n.googleSsoConfig&&(this.googleSsoConfig=n.googleSsoConfig),n.githubSsoConfig&&(this.githubSsoConfig=n.githubSsoConfig),this.emailVerificationRequired=null!==(t=n.emailVerificationRequired)&&void 0!==t&&t,n.jwtSecret&&localStorage.setItem("jwt_secret",n.jwtSecret),null!==(e=n.domainRestrictionEnabled)&&void 0!==e&&!e||this.validateDomain(n.organizationUrl)?("signin"===this.currentAction&&this.renderSignInForm(),[3,3]):(this.showUnauthorizedError(),[2]);case 2:return o=a.sent(),console.warn("Failed to fetch organization details:",o),i=this.api.getApiBase(),this.authUrls={signin:"".concat(i,"/auth/signin"),signup:"".concat(i,"/auth/signup"),verify:"".concat(i,"/auth/verify"),reset:"".concat(i,"/auth/reset"),update:"".concat(i,"/auth/update"),resend:"".concat(i,"/auth/resend"),successful:"".concat(i,"/auth/successful")},[3,3];case 3:return[2]}}))}))},t.prototype.hasGitHubCallback=function(){var t=new URLSearchParams(window.location.search),e=t.get("code"),n=t.get("state"),o=sessionStorage.getItem("github_oauth_state");return!!(e&&n&&o)},t.prototype.handleGitHubCallback=function(){var t,e,n,o;return i(this,void 0,void 0,(function(){var i,r,s,c,l,d,u,h,p,m,g,f,v,b;return a(this,(function(a){switch(a.label){case 0:(i=document.getElementById("github-callback-overlay"))&&i.remove(),r=new URLSearchParams(window.location.search),s=r.get("code"),c=r.get("state"),l=sessionStorage.getItem("github_oauth_state"),d=null;try{d=c?JSON.parse(decodeURIComponent(c)):null}catch(t){d={source:"signin",random:c}}if(!(s&&c&&l&&d&&d.random===l))return[3,6];(u=new URL(window.location.href)).searchParams.delete("code"),u.searchParams.delete("state"),window.history.replaceState({},document.title,u.pathname+u.search),this.showMessage("Processing GitHub authentication...","success"),a.label=1;case 1:return a.trys.push([1,4,5,6]),h="".concat(this.api.getApiBase(),"/auth/github"),[4,fetch(h,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({code:s,parentPublicKey:this.config.publicKey})})];case 2:return[4,(p=a.sent()).text()];case 3:m=a.sent(),g=void 0;try{g=JSON.parse(m)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===p.status&&g.success?(g.token&&this.storeTokens(g.token),g.user&&g.user.publicKey&&sessionStorage.setItem("publicKey",g.user.publicKey),g.user&&g.user.email&&sessionStorage.setItem("user_email",g.user.email),g.jwtSecret?localStorage.setItem("jwt_secret",g.jwtSecret):g.data&&g.data.jwtSecret&&localStorage.setItem("jwt_secret",g.data.jwtSecret),f=this.config.successAuthPath||(null===(t=this.authUrls)||void 0===t?void 0:t.successful)||"/",void 0,v="signup"===d.source?(null===(e=this.config.messages)||void 0===e?void 0:e.signupSuccess)||"Account created successfully!":(null===(n=this.config.messages)||void 0===n?void 0:n.signinSuccess)||"Welcome back!",this.dismissGoogleOneTap(),this.showMessage(v,"success"),setTimeout((function(){window.location.href=f}),150)):(b="signup"===d.source?"GitHub sign-up failed":"GitHub sign-in failed",this.showMessage((null===(o=g.error)||void 0===o?void 0:o.message)||b,"error")),[3,6];case 4:return a.sent(),b="signup"===d.source?"Network error during GitHub sign-up":"Network error during GitHub sign-in",this.showMessage(b,"error"),[3,6];case 5:return sessionStorage.removeItem("github_oauth_state"),[7];case 6:return[2]}}))}))},t.prototype.show=function(t){console.log("👁️ Widget.show() called with action:",t,"preset:",this.config.preset),this.currentAction&&this.currentAction!==t&&this.dismissGoogleOneTap(),this.currentAction=t,this.hasGitHubCallback()?this.handleGitHubCallback():("verify"===t?this.handleEmailVerification():"signin"===t?this.renderSignInForm():"signup"===t?this.renderSignUpForm():"reset"===t?this.renderResetPasswordForm():"update"===t?this.renderUpdatePasswordForm():"resend"===t&&this.renderResendConfirmationForm(),this.authUrls||this.initialize().catch((function(t){console.warn("Failed to fetch organization details:",t)})))},t.prototype.initializeContainer=function(){var t,e=document.getElementById(this.config.container);if(e||((e=document.createElement("div")).id=this.config.container,document.body.appendChild(e)),e.className="authiqa-container",null===(t=this.config.customization)||void 0===t?void 0:t.pageLayout){var n=this.config.customization.pageLayout;if(n.formPosition)switch(document.body.style.display="flex",document.body.style.minHeight="100vh",n.formPosition){case"top":document.body.style.alignItems="flex-start",document.body.style.justifyContent="center";break;case"bottom":document.body.style.alignItems="flex-end",document.body.style.justifyContent="center";break;case"left":document.body.style.alignItems="center",document.body.style.justifyContent="flex-start";break;case"right":document.body.style.alignItems="center",document.body.style.justifyContent="flex-end";break;default:document.body.style.alignItems="center",document.body.style.justifyContent="center"}n.formMarginTop&&(e.style.marginTop=n.formMarginTop),n.formMarginBottom&&(e.style.marginBottom=n.formMarginBottom),n.formMarginLeft&&(e.style.marginLeft=n.formMarginLeft),n.formMarginRight&&(e.style.marginRight=n.formMarginRight)}return this.config.customization||this.config.disableStyles||("dark"===this.config.theme?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme"),"none"!==this.config.theme&&e.setAttribute("data-theme",this.config.theme||"light")),e},t.prototype.createLabeledInput=function(t,e,n,o,i){void 0===i&&(i=!0);var a=document.createElement("div");a.className="labeled-input-container",a.classList.add("authiqa-labeled-input");var r=document.createElement("label");r.setAttribute("for","authiqa-".concat(e)),r.textContent=o,r.classList.add("authiqa-label");var s=document.createElement("input");return s.setAttribute("type",t),s.setAttribute("id","authiqa-".concat(e)),s.setAttribute("name",e),s.setAttribute("placeholder",n),s.setAttribute("required",i?"true":"false"),s.classList.add("authiqa-input"),"password"===t&&s.setAttribute("minlength","6"),a.appendChild(r),a.appendChild(s),{container:a,input:s}},t.prototype.createPasswordField=function(t,e,n){var o=document.createElement("div");if(o.classList.add("authiqa-labeled-input"),n){var i=document.createElement("label");i.setAttribute("for","authiqa-".concat(e)),i.textContent=n,i.classList.add("authiqa-label"),o.appendChild(i)}var a=document.createElement("div");a.className="password-field-container",a.classList.add("authiqa-password-container");var r=document.createElement("input");r.setAttribute("type","password"),r.setAttribute("id","authiqa-".concat(e)),r.setAttribute("name",e),r.setAttribute("placeholder",t),r.setAttribute("required","true"),r.setAttribute("minlength","6"),r.classList.add("authiqa-input"),a.appendChild(r);var s=document.createElement("button");s.setAttribute("type","button"),s.classList.add("password-toggle"),s.innerHTML="👁️",s.addEventListener("click",(function(){var t=r.getAttribute("type");r.setAttribute("type","password"===t?"text":"password"),s.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),a.appendChild(s);var c=document.createElement("div");c.classList.add("password-validation-container");var l=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];return l.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var o=document.createElement("span");o.classList.add("validation-text"),o.textContent=t.text,e.appendChild(n),e.appendChild(o),c.appendChild(e)})),o.appendChild(a),o.appendChild(c),r.addEventListener("input",(function(){var t=r.value;l.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))})),{container:o,input:r}},t.prototype.renderSignInForm=function(){var t,e,n,o,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w=this,x=this.initializeContainer();x.innerHTML="",window._authiqaGoogleButtonRendering=!1;var T=document.createElement("h1");T.classList.add("authiqa-title"),T.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.signinText)||"Sign in",x.appendChild(T);var S=document.createElement("form");S.classList.add("authiqa-form"),S.style.display="flex",S.style.flexDirection="column",S.style.gap="1rem";var P=this.createLabeledInput("email","email",(null===(r=null===(o=this.config.customization)||void 0===o?void 0:o.inputs)||void 0===r?void 0:r.emailPlaceholder)||"Email Address",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.emailLabel)||"Email"),C=P.container,k=P.input,A=this.createPasswordField((null===(d=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===d?void 0:d.passwordPlaceholder)||"Password","password",(null===(h=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===h?void 0:h.passwordLabel)||"Password"),q=A.container,L=A.input,z=this.config.resetAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.reset)||"#",E=null===(m=this.config.customization)||void 0===m?void 0:m.navLinks,I=(null==E?void 0:E.forgotPrompt)||"Forgot Password?",B=(null==E?void 0:E.forgotLinkText)||"Reset",M=document.createElement("div");M.className="forgot-password",M.innerHTML="".concat(I,' <a href="').concat(z,'">').concat(B,"</a>");var R=document.createElement("button");R.setAttribute("type","submit"),R.classList.add("authiqa-button"),R.textContent=(null===(f=null===(g=this.config.customization)||void 0===g?void 0:g.buttons)||void 0===f?void 0:f.signinText)||"Sign In",R.style.marginTop="0.5rem",S.appendChild(C),S.appendChild(q),S.appendChild(M),S.appendChild(R),S.addEventListener("submit",(function(t){return i(w,void 0,void 0,(function(){var e,n,o,i,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w=this;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),R.setAttribute("data-original-text",R.textContent||"Submit"),this.setLoadingState(R,!0,"signin"),e={email:k.value,password:L.value,parentPublicKey:this.config.publicKey},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signin"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return o=a.sent(),200===n.status?o.success&&"data"in o&&(this.storeTokens(o.data.token),o.data.user&&o.data.user.publicKey&&sessionStorage.setItem("publicKey",o.data.user.publicKey),o.data.user&&o.data.user.email&&sessionStorage.setItem("user_email",o.data.user.email),o.data.jwtSecret&&localStorage.setItem("jwt_secret",o.data.jwtSecret),(null===(h=o.data.passwordStatus)||void 0===h?void 0:h.expired)?(r=this.config.resetAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.reset)||"",this.showMessage("Your password has expired. Please update it now.","warning",r)):void 0!==(null===(m=o.data.passwordStatus)||void 0===m?void 0:m.daysUntilExpiry)&&o.data.passwordStatus.daysUntilExpiry<=14?(i=o.data.passwordStatus.daysUntilExpiry,r=this.config.resetAuthPath||(null===(g=this.authUrls)||void 0===g?void 0:g.reset)||"",s="Your password will expire in ".concat(i," day").concat(1!==i?"s":"",". Please update it soon."),i<=3?(this.showMessage(s,"warning"),setTimeout((function(){var t,e=w.config.successAuthPath||(null===(t=w.authUrls)||void 0===t?void 0:t.successful)||"";window.location.href=e}),3e3)):(c=this.config.successAuthPath||(null===(f=this.authUrls)||void 0===f?void 0:f.successful)||"",this.showMessage(s,"warning",c))):(l=this.config.successAuthPath||(null===(v=this.authUrls)||void 0===v?void 0:v.successful)||"",this.dismissGoogleOneTap(),this.showMessage((null===(b=this.config.messages)||void 0===b?void 0:b.signinSuccess)||"Welcome back!","success",l))):!o.success&&"error"in o?"EMAIL_NOT_VERIFIED"===o.error.code&&this.emailVerificationRequired?(d=this.config.resendAuthPath||(null===(y=this.authUrls)||void 0===y?void 0:y.resend)||"",this.showMessage("".concat(o.error.message," Please verify your email before signing in."),"error",d)):this.showMessage(o.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return u=a.sent(),console.error("Signin network error:",u),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(R,!1,"signin"),[7];case 6:return[2]}}))}))})),x.appendChild(S);var _=function(){var t;if((null===(t=w.githubSsoConfig)||void 0===t?void 0:t.enabled)&&w.githubSsoConfig.clientId&&!document.getElementById("github-button-container")){var e=document.createElement("div");e.id="github-button-container",e.style.margin="0.5rem 0 0 0",e.style.display="flex",e.style.justifyContent="center";var n=document.createElement("button");n.type="button",n.className="authiqa-github-button",n.innerHTML='<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Continue with GitHub',n.onclick=function(){var t=w.githubSsoConfig.clientId,e=w.config.signinAuthPath?w.config.signinAuthPath.startsWith("http")?w.config.signinAuthPath:window.location.origin+w.config.signinAuthPath:window.location.origin+window.location.pathname,n={source:"signin",random:Math.random().toString(36).substring(2,15)},o=encodeURIComponent(JSON.stringify(n));sessionStorage.setItem("github_oauth_state",n.random);var i="https://github.com/login/oauth/authorize?client_id=".concat(encodeURIComponent(t),"&redirect_uri=").concat(encodeURIComponent(e),"&scope=").concat(encodeURIComponent("read:user user:email"),"&state=").concat(encodeURIComponent(o));window.location.href=i},e.appendChild(n),S.insertBefore(e,R.nextSibling)}};if(_(),!(null===(v=this.githubSsoConfig)||void 0===v?void 0:v.enabled)||!this.githubSsoConfig.clientId){var N=setInterval((function(){var t;(null===(t=w.githubSsoConfig)||void 0===t?void 0:t.enabled)&&w.githubSsoConfig.clientId&&!document.getElementById("github-button-container")&&(_(),clearInterval(N))}),200);setTimeout((function(){return clearInterval(N)}),5e3)}var O=function(){var t,e,n,o;if((null===(t=w.googleSsoConfig)||void 0===t?void 0:t.enabled)&&w.googleSsoConfig.clientId&&!document.getElementById("google-button-container")){if((null===(o=null===(n=null===(e=window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.id)||void 0===o?void 0:o.cancel)&&window.google.accounts.id.cancel(),window._authiqaGoogleButtonRendering)return;if(window._authiqaGoogleButtonRendering=!0,!document.getElementById("google-identity-services")){var r=document.createElement("script");r.src="https://accounts.google.com/gsi/client",r.async=!0,r.defer=!0,r.id="google-identity-services",document.head.appendChild(r)}void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),void 0===window._authiqaGoogleOneTapSuccessfulAuth&&(window._authiqaGoogleOneTapSuccessfulAuth=!1);var s=function(){if(window.google&&window.google.accounts){window.google.accounts.id.initialize({client_id:w.googleSsoConfig.clientId,callback:function(t){return i(w,void 0,void 0,(function(){var e,n,o,i,r,s,c,l;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];n="".concat(this.api.getApiBase(),"/auth/google"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(o=a.sent()).text()];case 3:i=a.sent(),r=void 0;try{r=JSON.parse(i)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===o.status&&r.success?(r.token&&this.storeTokens(r.token),r.user&&r.user.publicKey&&sessionStorage.setItem("publicKey",r.user.publicKey),r.user&&r.user.email&&sessionStorage.setItem("user_email",r.user.email),r.jwtSecret?localStorage.setItem("jwt_secret",r.jwtSecret):r.data&&r.data.jwtSecret&&localStorage.setItem("jwt_secret",r.data.jwtSecret),s=this.config.successAuthPath||(null===(c=this.authUrls)||void 0===c?void 0:c.successful)||"/",this.dismissGoogleOneTap(),setTimeout((function(){window.location.href=s}),150)):this.showMessage((null===(l=r.error)||void 0===l?void 0:l.message)||"Google sign-in failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-in","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup",auto_select:!1,use_fedcm_for_button:!0,context:"signin",button_auto_select:!1});var t=document.createElement("div");t.id="google-button-container",t.style.margin="0.5rem 0 0 0",t.style.direction="ltr";var e=document.getElementById("github-button-container");e&&e.parentNode?e.parentNode.insertBefore(t,e.nextSibling):S.insertBefore(t,R.nextSibling),setTimeout((function(){window.google.accounts.id.renderButton(t,{theme:"outline",size:"large",text:"continue_with",shape:"rectangular",logo_alignment:"left",auto_prompt:!1,auto_select:!1,type:"standard"}),setTimeout((function(){window._authiqaGoogleButtonRendering=!1}),200)}),0),setTimeout((function(){!w.shouldEnableGoogleOneTap()||window._authiqaGoogleOneTapDismissed||w.hasGoogleOneTapSuccessfulAuth()||(window.google.accounts.id.prompt((function(t){t.isSkippedMoment()?window._authiqaGoogleOneTapDismissed=!0:t.isDismissedMoment()&&(window._authiqaGoogleOneTapDismissed=!0,"credential_returned"===t.getDismissedReason()&&w.markGoogleOneTapSuccessful())})),window._authiqaGoogleOneTapDismissed=!0)}),1500)}else setTimeout(s,100)};try{s()}catch(t){console.error("Error initializing Google services:",t),window._authiqaGoogleButtonRendering=!1}}};if(O(),!(null===(b=this.googleSsoConfig)||void 0===b?void 0:b.enabled)||!this.googleSsoConfig.clientId){var U=setInterval((function(){var t;(null===(t=w.googleSsoConfig)||void 0===t?void 0:t.enabled)&&w.googleSsoConfig.clientId&&!document.getElementById("google-button-container")&&(O(),clearInterval(U))}),200);setTimeout((function(){return clearInterval(U)}),5e3)}var H=this.config.signupAuthPath||(null===(y=this.authUrls)||void 0===y?void 0:y.signup)||"#",j=(null==E?void 0:E.signupPrompt)||"Don't have an account?",V=(null==E?void 0:E.signupLinkText)||"Sign Up",F=document.createElement("div");F.className="alternate-action",F.innerHTML="".concat(j,' <a href="').concat(H,'">').concat(V,"</a>"),S.appendChild(F)},t.prototype.renderSignUpForm=function(){var t,e,n,o,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w,x,T,S,P,C=this,k=this.initializeContainer();k.innerHTML="",window._authiqaGoogleSignupButtonRendering=!1;var A=null===(t=this.config.customization)||void 0===t?void 0:t.navLinks,q=document.createElement("h1");q.classList.add("authiqa-title"),q.textContent=(null===(o=null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.typography)||void 0===n?void 0:n.titleText)||void 0===o?void 0:o.signupText)||"Sign up",k.appendChild(q);var L=document.createElement("form");L.classList.add("authiqa-form"),L.style.display="flex",L.style.flexDirection="column",L.style.gap="1rem";var z=this.createLabeledInput("text","username",(null===(s=null===(r=this.config.customization)||void 0===r?void 0:r.inputs)||void 0===s?void 0:s.usernamePlaceholder)||"Username",(null===(l=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===l?void 0:l.usernameLabel)||"Username"),E=z.container,I=z.input,B=this.createLabeledInput("email","email",(null===(u=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===u?void 0:u.emailPlaceholder)||"Email Address",(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.inputs)||void 0===p?void 0:p.emailLabel)||"Email"),M=B.container,R=B.input,_=this.createPasswordField((null===(g=null===(m=this.config.customization)||void 0===m?void 0:m.inputs)||void 0===g?void 0:g.passwordPlaceholder)||"Password","password",(null===(v=null===(f=this.config.customization)||void 0===f?void 0:f.inputs)||void 0===v?void 0:v.passwordLabel)||"Password"),N=_.container,O=_.input;L.appendChild(E),L.appendChild(M),L.appendChild(N);var U=document.createElement("div");U.classList.add("terms-container"),U.style.display="flex",U.style.alignItems="flex-start",U.style.marginBottom="1rem";var H=document.createElement("input");H.setAttribute("type","checkbox"),H.setAttribute("id","terms"),H.setAttribute("name","terms"),H.setAttribute("required","required"),H.style.marginTop="0.25rem",H.style.marginRight="0.5rem";var j=document.createElement("label");j.setAttribute("for","terms"),j.style.flex="1",j.style.margin="0",j.style.padding="0",j.style.color="#525252",j.style.fontSize="0.875rem",j.style.lineHeight="1.4";var V=(null===(y=null===(b=this.config.customization)||void 0===b?void 0:b.typography)||void 0===y?void 0:y.termsText)||{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"}},F=V.agreePrefix,D=V.andConnector,G=V.defaultPrefix,W=V.linkText;j.innerHTML="".concat(F,' <a href="').concat(this.config.termsAndConditions||"#",'">').concat(W.terms,'</a> <a href="').concat(this.config.privacy||"#",'">').concat(W.privacy,"</a> ").concat(D," ").concat(G,' <a href="').concat(this.config.notificationSettings||"#",'">').concat(W.notifications,"</a>."),U.appendChild(H),U.appendChild(j);var K=document.createElement("button");K.setAttribute("type","submit"),K.classList.add("authiqa-button");var J=(null===(x=null===(w=this.config.customization)||void 0===w?void 0:w.buttons)||void 0===x?void 0:x.signupText)||"Create Account";K.textContent=J,L.appendChild(U),L.appendChild(K),L.addEventListener("submit",(function(t){return i(C,void 0,void 0,(function(){var e,n,o,i,r,s,c,l,d,u,h,p,m,g,f;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),!H.checked)return this.showMessage("Please accept the terms and conditions","error"),[2];if(!(e=this.validatePassword(O.value)).isValid&&e.error)return this.showMessage("".concat(e.error.message," (").concat(e.error.code,")"),"error"),[2];K.setAttribute("data-original-text",K.textContent||"Submit"),this.setLoadingState(K,!0,"signup"),n={username:I.value,email:R.value,password:O.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)})];case 2:return[4,(o=a.sent()).json()];case 3:switch(i=a.sent(),o.status){case 200:i.success&&i.data&&(i.token?this.storeTokens(i.token):i.data&&i.data.token&&this.storeTokens(i.data.token),i.jwtSecret?localStorage.setItem("jwt_secret",i.jwtSecret):i.data&&i.data.jwtSecret&&localStorage.setItem("jwt_secret",i.data.jwtSecret),i.data.data&&i.data.data.publicKey&&sessionStorage.setItem("publicKey",i.data.data.publicKey),i.data.data&&i.data.data.email&&sessionStorage.setItem("user_email",i.data.data.email),r=void 0,s=void 0,this.emailVerificationRequired?(r=this.config.resendAuthPath||(null===(p=this.authUrls)||void 0===p?void 0:p.resend)||"",s=(null===(m=this.config.messages)||void 0===m?void 0:m.signupSuccess)||"Account created successfully! Please check your email to verify your account before signing in."):(r=this.config.successAuthPath||(null===(g=this.authUrls)||void 0===g?void 0:g.successful)||"",s=(null===(f=this.config.messages)||void 0===f?void 0:f.signinSuccess)||"Successfully signed up! Welcome to your account."),this.dismissGoogleOneTap(),this.showMessage(s,"success",r));break;case 409:switch((c=i.error).code){case"EMAIL_ALREADY_EXISTS":case"USERNAME_ALREADY_EXISTS":case"DUPLICATE_EMAIL_USERNAME_COMBO":this.showMessage("".concat(c.message," (").concat(c.code,")"),"error");break;default:this.showMessage("".concat(c.message),"error")}break;case 400:switch((l=i.error).code){case"MISSING_REQUEST_BODY":case"MISSING_REQUIRED_FIELDS":case"INVALID_EMAIL_FORMAT":case"INVALID_PASSWORD_FORMAT":case"INVALID_USERNAME_FORMAT":case"MISSING_PARENT_PUBLIC_KEY":this.showMessage("".concat(l.message," (").concat(l.code,")"),"error");break;default:this.showMessage("".concat(l.message),"error")}break;case 401:"INVALID_PARENT_PUBLIC_KEY"===(d=i.error).code?this.showMessage("".concat(d.message," (").concat(d.code,")"),"error"):this.showMessage("".concat(d.message),"error");break;case 403:"PARENT_ACCOUNT_INACTIVE"===(u=i.error).code?this.showMessage("".concat(u.message," (").concat(u.code,")"),"error"):this.showMessage("".concat(u.message),"error");break;case 500:this.showMessage("An internal server error occurred. Please try again later.","error");break;default:this.showMessage("An unexpected error occurred. Please try again.","error")}return[3,6];case 4:return h=a.sent(),console.error("Signup network error:",h),this.showMessage("Network error: Unable to connect to the server. Please check your connection and try again.","error"),[3,6];case 5:return this.setLoadingState(K,!1,"signup"),[7];case 6:return[2]}}))}))})),k.appendChild(L);var Y=function(){var t;if((null===(t=C.githubSsoConfig)||void 0===t?void 0:t.enabled)&&C.githubSsoConfig.clientId&&!document.getElementById("github-signup-button-container")){var e=document.createElement("div");e.id="github-signup-button-container",e.style.margin="0.5rem 0 0 0",e.style.display="flex",e.style.justifyContent="center";var n=document.createElement("button");n.type="button",n.className="authiqa-github-button",n.innerHTML='<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Continue with GitHub',n.onclick=function(){var t=C.githubSsoConfig.clientId,e=C.config.signinAuthPath?C.config.signinAuthPath.startsWith("http")?C.config.signinAuthPath:window.location.origin+C.config.signinAuthPath:window.location.origin+window.location.pathname,n={source:"signup",random:Math.random().toString(36).substring(2,15)},o=encodeURIComponent(JSON.stringify(n));sessionStorage.setItem("github_oauth_state",n.random);var i="https://github.com/login/oauth/authorize?client_id=".concat(encodeURIComponent(t),"&redirect_uri=").concat(encodeURIComponent(e),"&scope=").concat(encodeURIComponent("read:user user:email"),"&state=").concat(encodeURIComponent(o));window.location.href=i},e.appendChild(n),L.insertBefore(e,K.nextSibling)}};if(Y(),!(null===(T=this.githubSsoConfig)||void 0===T?void 0:T.enabled)||!this.githubSsoConfig.clientId){var X=setInterval((function(){var t;(null===(t=C.githubSsoConfig)||void 0===t?void 0:t.enabled)&&C.githubSsoConfig.clientId&&!document.getElementById("github-signup-button-container")&&(Y(),clearInterval(X))}),200);setTimeout((function(){return clearInterval(X)}),5e3)}var Z=function(){var t,e,n,o;if((null===(t=C.googleSsoConfig)||void 0===t?void 0:t.enabled)&&C.googleSsoConfig.clientId&&!document.getElementById("google-signup-button-container")){if((null===(o=null===(n=null===(e=window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.id)||void 0===o?void 0:o.cancel)&&window.google.accounts.id.cancel(),window._authiqaGoogleSignupButtonRendering)return;if(window._authiqaGoogleSignupButtonRendering=!0,!document.getElementById("google-identity-services")){var r=document.createElement("script");r.src="https://accounts.google.com/gsi/client",r.async=!0,r.defer=!0,r.id="google-identity-services",document.head.appendChild(r)}void 0===window._authiqaGoogleOneTapDismissed&&(window._authiqaGoogleOneTapDismissed=!1),void 0===window._authiqaGoogleOneTapSuccessfulAuth&&(window._authiqaGoogleOneTapSuccessfulAuth=!1);var s=function(){if(window.google&&window.google.accounts){window.google.accounts.id.initialize({client_id:C.googleSsoConfig.clientId,callback:function(t){return i(C,void 0,void 0,(function(){var e,n,o,i,r,s,c,l;return a(this,(function(a){switch(a.label){case 0:if(!(e=t.credential))return[2];n="".concat(this.api.getApiBase(),"/auth/google"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({idToken:e,parentPublicKey:this.config.publicKey})})];case 2:return[4,(o=a.sent()).text()];case 3:i=a.sent(),r=void 0;try{r=JSON.parse(i)}catch(t){return this.showMessage("Invalid response format from server","error"),[2]}return 200===o.status&&r.success?(r.token&&this.storeTokens(r.token),r.user&&r.user.publicKey&&sessionStorage.setItem("publicKey",r.user.publicKey),r.user&&r.user.email&&sessionStorage.setItem("user_email",r.user.email),r.jwtSecret?localStorage.setItem("jwt_secret",r.jwtSecret):r.data&&r.data.jwtSecret&&localStorage.setItem("jwt_secret",r.data.jwtSecret),s=this.config.successAuthPath||(null===(c=this.authUrls)||void 0===c?void 0:c.successful)||"/",this.dismissGoogleOneTap(),setTimeout((function(){window.location.href=s}),150)):this.showMessage((null===(l=r.error)||void 0===l?void 0:l.message)||"Google sign-up failed","error"),[3,5];case 4:return a.sent(),this.showMessage("Network error during Google sign-up","error"),[3,5];case 5:return[2]}}))}))},ux_mode:"popup",auto_select:!1,use_fedcm_for_button:!0,context:"signup",button_auto_select:!1});var t=document.createElement("div");t.id="google-signup-button-container",t.style.margin="0.5rem 0 0 0",t.style.direction="ltr";var e=document.getElementById("github-signup-button-container");e&&e.parentNode?e.parentNode.insertBefore(t,e.nextSibling):L.insertBefore(t,K.nextSibling),setTimeout((function(){window.google.accounts.id.renderButton(t,{theme:"outline",size:"large",text:"continue_with",shape:"rectangular",logo_alignment:"left",auto_prompt:!1,auto_select:!1,type:"standard"}),setTimeout((function(){window._authiqaGoogleSignupButtonRendering=!1}),200)}),0),setTimeout((function(){!C.shouldEnableGoogleOneTap()||window._authiqaGoogleOneTapDismissed||C.hasGoogleOneTapSuccessfulAuth()||(window.google.accounts.id.prompt((function(t){t.isSkippedMoment()?window._authiqaGoogleOneTapDismissed=!0:t.isDismissedMoment()&&(window._authiqaGoogleOneTapDismissed=!0,"credential_returned"===t.getDismissedReason()&&C.markGoogleOneTapSuccessful())})),window._authiqaGoogleOneTapDismissed=!0)}),1500)}else setTimeout(s,100)};try{s()}catch(t){console.error("Error initializing Google signup services:",t),window._authiqaGoogleSignupButtonRendering=!1}}};if(Z(),!(null===(S=this.googleSsoConfig)||void 0===S?void 0:S.enabled)||!this.googleSsoConfig.clientId){var $=setInterval((function(){var t;(null===(t=C.googleSsoConfig)||void 0===t?void 0:t.enabled)&&C.googleSsoConfig.clientId&&!document.getElementById("google-signup-button-container")&&(Z(),clearInterval($))}),200);setTimeout((function(){return clearInterval($)}),5e3)}var Q=this.config.signinAuthPath||(null===(P=this.authUrls)||void 0===P?void 0:P.signin)||"#",tt=(null==A?void 0:A.signinPrompt)||"Already have an account?",et=(null==A?void 0:A.signinLinkText)||"Sign In",nt=document.createElement("div");nt.className="alternate-action",nt.innerHTML="".concat(tt,' <a href="').concat(Q,'">').concat(et,"</a>"),L.appendChild(nt)},t.prototype.validatePassword=function(t){var e=t.length>=8,n=/[A-Z]/.test(t),o=/[0-9]/.test(t),i=/[!@#$%^&*(),.?":{}|<>]/.test(t);if(!(e&&n&&o&&i)){var a="Password must contain:";return e||(a+=" at least 8 characters,"),n||(a+=" at least one uppercase letter,"),o||(a+=" at least one number,"),i||(a+=" at least one special character,"),{isValid:!1,error:{code:"INVALID_PASSWORD_FORMAT",message:a=a.replace(/,$/,"")}}}return{isValid:!0}},t.prototype.renderResetPasswordForm=function(){var t,e,n,o,r,s,c,l,d,u,h,p,m,g,f=this,v=this.initializeContainer();v.innerHTML="";var b=document.createElement("h1");b.classList.add("authiqa-title"),b.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.resetText)||"Reset Password",(null===(s=null===(r=null===(o=this.config.customization)||void 0===o?void 0:o.typography)||void 0===r?void 0:r.subtitleText)||void 0===s?void 0:s.resetText)&&b.setAttribute("data-subtitle",this.config.customization.typography.subtitleText.resetText),v.appendChild(b);var y=document.createElement("form");y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem";var w=this.createLabeledInput("email","email",(null===(l=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===l?void 0:l.emailPlaceholder)||"Email Address",(null===(u=null===(d=this.config.customization)||void 0===d?void 0:d.inputs)||void 0===u?void 0:u.emailLabel)||"Email"),x=w.container,T=w.input;y.appendChild(x);var S=document.createElement("button");S.setAttribute("type","submit"),S.classList.add("authiqa-button"),S.textContent=(null===(p=null===(h=this.config.customization)||void 0===h?void 0:h.buttons)||void 0===p?void 0:p.resetText)||"Reset Password",y.appendChild(S);var P=this.config.signinAuthPath||(null===(m=this.authUrls)||void 0===m?void 0:m.signin)||"#",C=null===(g=this.config.customization)||void 0===g?void 0:g.navLinks,k=(null==C?void 0:C.backToSigninPrompt)||"Back to Sign In?",A=document.createElement("div");A.className="alternate-action",A.innerHTML="".concat(k,' <a href="').concat(P,'">Sign In</a>'),y.appendChild(A),y.addEventListener("submit",(function(t){return i(f,void 0,void 0,(function(){var e,n,o,i,r;return a(this,(function(a){switch(a.label){case 0:t.preventDefault(),this.setLoadingState(S,!0,"reset"),e={email:T.value,parentPublicKey:this.config.publicKey,updatePasswordPath:this.config.updatePasswordPath},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return o=a.sent(),200===n.status?o.success&&o.data&&this.showMessage((null===(r=this.config.messages)||void 0===r?void 0:r.resetSuccess)||o.data.message,"success"):!o.success&&o.error?this.showMessage(o.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return i=a.sent(),console.error("Reset password network error:",i),this.showMessage("Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(S,!1,"reset"),[7];case 6:return[2]}}))}))})),v.appendChild(y)},t.prototype.renderUpdatePasswordForm=function(){var t,e,n,o,r,s,c,l,d,u,h,p,m,g=this,f=this.initializeContainer();f.innerHTML="";var v=document.createElement("h1");v.classList.add("authiqa-title"),v.textContent=(null===(n=null===(e=null===(t=this.config.customization)||void 0===t?void 0:t.typography)||void 0===e?void 0:e.titleText)||void 0===n?void 0:n.updateText)||"Update Password",f.appendChild(v);var b=new URLSearchParams(window.location.search).get("token"),y=document.createElement("form");if(y.classList.add("authiqa-form"),y.style.display="flex",y.style.flexDirection="column",y.style.gap="1rem",b){var w=document.createElement("input");w.setAttribute("type","hidden"),w.setAttribute("name","token"),w.value=b,y.appendChild(w)}else console.warn("No token found in URL - password reset may fail");var x=document.createElement("div");x.classList.add("authiqa-labeled-input");var T=document.createElement("label");T.setAttribute("for","authiqa-newPassword"),T.textContent=(null===(r=null===(o=this.config.customization)||void 0===o?void 0:o.inputs)||void 0===r?void 0:r.passwordLabel)||"New Password",T.classList.add("authiqa-label"),x.appendChild(T);var S=document.createElement("div");S.className="password-field-container",S.classList.add("authiqa-password-container");var P=document.createElement("input");P.setAttribute("type","password"),P.setAttribute("id","authiqa-newPassword"),P.setAttribute("name","newPassword"),P.setAttribute("placeholder",(null===(c=null===(s=this.config.customization)||void 0===s?void 0:s.inputs)||void 0===c?void 0:c.passwordPlaceholder)||"New Password"),P.setAttribute("required","true"),P.setAttribute("minlength","6"),P.classList.add("authiqa-input"),S.appendChild(P);var C=document.createElement("button");C.setAttribute("type","button"),C.classList.add("password-toggle"),C.innerHTML="👁️",C.addEventListener("click",(function(){var t=P.getAttribute("type");P.setAttribute("type","password"===t?"text":"password"),C.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),S.appendChild(C),x.appendChild(S);var k=document.createElement("div");k.classList.add("authiqa-labeled-input");var A=document.createElement("label");A.setAttribute("for","authiqa-confirmPassword"),A.textContent=(null===(d=null===(l=this.config.customization)||void 0===l?void 0:l.inputs)||void 0===d?void 0:d.confirmPasswordLabel)||"Confirm Password",A.classList.add("authiqa-label"),k.appendChild(A);var q=document.createElement("div");q.className="password-field-container",q.classList.add("authiqa-password-container");var L=document.createElement("input");L.setAttribute("type","password"),L.setAttribute("id","authiqa-confirmPassword"),L.setAttribute("name","confirmPassword"),L.setAttribute("placeholder",(null===(h=null===(u=this.config.customization)||void 0===u?void 0:u.inputs)||void 0===h?void 0:h.confirmPasswordPlaceholder)||"Confirm Password"),L.setAttribute("required","true"),L.classList.add("authiqa-input"),q.appendChild(L);var z=document.createElement("button");z.setAttribute("type","button"),z.classList.add("password-toggle"),z.innerHTML="👁️",z.addEventListener("click",(function(){var t=L.getAttribute("type");L.setAttribute("type","password"===t?"text":"password"),z.innerHTML="password"===t?"👁️‍🗨️":"👁️"})),q.appendChild(z),k.appendChild(q);var E=document.createElement("div");E.classList.add("password-validation-container");var I=[{id:"length",text:"8+ Characters long",check:function(t){return t.length>=8}},{id:"uppercase",text:"1+ Uppercase letter",check:function(t){return/[A-Z]/.test(t)}},{id:"special",text:"1+ Special characters",check:function(t){return/[!@#$%^&*(),.?":{}|<>]/.test(t)}},{id:"number",text:"1+ Number",check:function(t){return/[0-9]/.test(t)}}];I.forEach((function(t){var e=document.createElement("div");e.classList.add("validation-item"),e.id="validation-".concat(t.id);var n=document.createElement("span");n.classList.add("validation-dot"),n.textContent="•";var o=document.createElement("span");o.classList.add("validation-text"),o.textContent=t.text,e.appendChild(n),e.appendChild(o),E.appendChild(e)})),y.appendChild(x),y.appendChild(k),y.appendChild(E),P.addEventListener("input",(function(){var t=P.value;I.forEach((function(e){var n=document.getElementById("validation-".concat(e.id));n&&(e.check(t)?n.classList.add("valid"):n.classList.remove("valid"))}))}));var B=function(){L.value&&(P.value!==L.value?L.setCustomValidity("Passwords do not match"):L.setCustomValidity(""))};P.addEventListener("input",B),L.addEventListener("input",B);var M=document.createElement("button");M.setAttribute("type","submit"),M.classList.add("authiqa-button"),M.textContent=(null===(m=null===(p=this.config.customization)||void 0===p?void 0:p.buttons)||void 0===m?void 0:m.updateText)||"Update Password",y.appendChild(M),y.addEventListener("submit",(function(t){return i(g,void 0,void 0,(function(){var e,n,o,i,r,s;return a(this,(function(a){switch(a.label){case 0:if(t.preventDefault(),P.value!==L.value)return this.showMessage("Passwords do not match","error"),[2];this.setLoadingState(M,!0,"update"),e={token:b,password:P.value},a.label=1;case 1:return a.trys.push([1,4,5,6]),[4,fetch("".concat(this.api.getApiBase(),"/auth/update-password"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})];case 2:return[4,(n=a.sent()).json()];case 3:return o=a.sent(),200===n.status?o.success&&o.data&&(i=this.config.signinAuthPath||(null===(r=this.authUrls)||void 0===r?void 0:r.signin),this.showMessage((null===(s=this.config.messages)||void 0===s?void 0:s.updateSuccess)||"Password updated successfully!","success",i)):!o.success&&o.error?this.showMessage(o.error.message,"error"):this.showMessage("An unexpected error occurred","error"),[3,6];case 4:return a.sent(),this.showMessage("Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(M,!1,"update"),[7];case 6:return[2]}}))}))})),f.appendChild(y)},t.prototype.renderResendConfirmationForm=function(){var t=this.initializeContainer();t.innerHTML="",this.isUserAuthenticated()?this.renderSimpleVerifyButton(t):this.renderTraditionalResendForm(t)},t.prototype.renderSimpleVerifyButton=function(t){var e,n,o=document.createElement("button");o.classList.add("authiqa-button"),o.textContent=(null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.buttons)||void 0===n?void 0:n.resendText)||"Verify Email",o.style.width="100%",o.style.maxWidth="300px",o.style.margin="0 auto",o.style.display="block",this.attachResendButtonHandler(o,!0),t.appendChild(o)},t.prototype.renderTraditionalResendForm=function(t){var e,n,o,r,s,c,l,d,u,h,p,m=this,g=document.createElement("h1");g.classList.add("authiqa-title"),g.textContent=(null===(o=null===(n=null===(e=this.config.customization)||void 0===e?void 0:e.typography)||void 0===n?void 0:n.titleText)||void 0===o?void 0:o.resendText)||"Resend Confirmation",t.appendChild(g);var f=document.createElement("form");f.classList.add("authiqa-form"),f.style.display="flex",f.style.flexDirection="column",f.style.gap="1rem";var v=new URLSearchParams(window.location.search).get("email"),b=this.createLabeledInput("email","email",(null===(s=null===(r=this.config.customization)||void 0===r?void 0:r.inputs)||void 0===s?void 0:s.emailPlaceholder)||"Email Address",(null===(l=null===(c=this.config.customization)||void 0===c?void 0:c.inputs)||void 0===l?void 0:l.emailLabel)||"Email"),y=b.container,w=b.input;v&&(w.value=v),f.appendChild(y);var x=document.createElement("button");x.setAttribute("type","submit"),x.classList.add("authiqa-button"),x.textContent=(null===(u=null===(d=this.config.customization)||void 0===d?void 0:d.buttons)||void 0===u?void 0:u.resendText)||"Verify Email",f.appendChild(x);var T=this.config.signinAuthPath||(null===(h=this.authUrls)||void 0===h?void 0:h.signin)||"#",S=null===(p=this.config.customization)||void 0===p?void 0:p.navLinks,P=(null==S?void 0:S.backToSigninPrompt)||"Back to Sign In?",C=document.createElement("div");C.className="alternate-action",C.innerHTML="".concat(P,' <a href="').concat(T,'">Sign In</a>'),f.appendChild(C),f.addEventListener("submit",(function(t){return i(m,void 0,void 0,(function(){return a(this,(function(e){return t.preventDefault(),this.handleTraditionalResendSubmit(w,x),[2]}))}))})),t.appendChild(f)},t.prototype.attachResendButtonHandler=function(t,e){var n=this;t.addEventListener("click",(function(o){return i(n,void 0,void 0,(function(){var n,i,r,s,c,l,d,u;return a(this,(function(a){switch(a.label){case 0:if(o.preventDefault(),e&&(n=localStorage.getItem("authiqa_last_resend_time"),i=Date.now(),n&&i-parseInt(n)<6e4))return r=Math.ceil((6e4-(i-parseInt(n)))/1e3),this.showMessage("Please wait ".concat(r," seconds before requesting another email."),"error"),[2];this.setLoadingState(t,!0,"resend"),a.label=1;case 1:return a.trys.push([1,4,5,6]),(s=this.getStoredToken())?[4,fetch("".concat(this.api.getApiBase(),"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify({verifyAuthPath:this.config.verifyAuthPath})})]:(this.showMessage("Authentication token not found. Please sign in again.","error"),[2]);case 2:return[4,(c=a.sent()).json()];case 3:if(l=a.sent(),200!==c.status||!l.success)throw new Error((null===(u=l.error)||void 0===u?void 0:u.message)||"Failed to send verification email");return this.showMessage("Verification email sent successfully!","success"),localStorage.setItem("authiqa_last_resend_time",Date.now().toString()),[3,6];case 4:return d=a.sent(),this.showMessage(d.message||"An error occurred","error"),[3,6];case 5:return this.setLoadingState(t,!1,"resend"),[7];case 6:return[2]}}))}))}))},t.prototype.handleTraditionalResendSubmit=function(t,e){var n,o,r;return i(this,void 0,void 0,(function(){var i,s,c,l;return a(this,(function(a){switch(a.label){case 0:if(!t||!t.value)return this.showMessage("Please enter your email address.","error"),[2];this.setLoadingState(e,!0,"resend"),a.label=1;case 1:return a.trys.push([1,4,5,6]),i={email:t.value,parentPublicKey:this.config.publicKey,verifyAuthPath:this.config.verifyAuthPath},[4,fetch("".concat(this.api.getApiBase(),"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})];case 2:return[4,(s=a.sent()).json()];case 3:if(c=a.sent(),200!==s.status||!c.success)throw new Error((null===(r=c.error)||void 0===r?void 0:r.message)||"Failed to send verification email");return this.showMessage((null===(n=this.config.messages)||void 0===n?void 0:n.resendSuccess)||(null===(o=c.data)||void 0===o?void 0:o.message)||"Verification email sent successfully!","success"),[3,6];case 4:return l=a.sent(),this.showMessage(l.message||"Network error: Unable to connect to the server","error"),[3,6];case 5:return this.setLoadingState(e,!1,"resend"),[7];case 6:return[2]}}))}))},t.prototype.renderVerificationStatus=function(t,e){var n,o,i=this.initializeContainer();i.innerHTML="";var a=document.createElement("div");a.className="verification-status";var r=document.createElement("h1");r.textContent="Email Verification";var s=document.createElement("div");if("loading"===t){var c=document.createElement("div");c.className="verification-loader",s.appendChild(c),e=(null===(n=this.config.messages)||void 0===n?void 0:n.verificationLoading)||e}else{var l=document.createElement("div");l.className="verification-icon ".concat(t),l.innerHTML="success"===t?"✓":"✕",s.appendChild(l),"success"===t&&(e=(null===(o=this.config.messages)||void 0===o?void 0:o.verificationSuccess)||e)}var d=document.createElement("p");d.textContent=e,a.appendChild(r),a.appendChild(s),a.appendChild(d),i.appendChild(a)},t.prototype.handleEmailVerification=function(){var t,e,n,o;return i(this,void 0,void 0,(function(){var i,r,s,c,l,d,u,h,p;return a(this,(function(a){switch(a.label){case 0:if(i=new URLSearchParams(window.location.search),!(r=i.get("token")))return this.renderVerificationStatus("error","Invalid verification token (INVALID_TOKEN)"),[2];this.renderVerificationStatus("loading",(null===(t=this.config.messages)||void 0===t?void 0:t.verificationLoading)||"Verifying your email address..."),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch("".concat(this.api.getApiBase(),"/auth/confirm-email?token=").concat(encodeURIComponent(r)),{headers:{"X-Public-Key":this.config.publicKey,"Content-Type":"application/json"}})];case 2:return[4,(s=a.sent()).json()];case 3:switch(c=a.sent(),s.status){case 200:c.success&&c.data&&(this.renderVerificationStatus("success",(null===(e=this.config.messages)||void 0===e?void 0:e.verificationSuccess)||c.data.message||"Email verified successfully"),l=this.emailVerificationRequired?this.config.signinAuthPath||(null===(n=this.authUrls)||void 0===n?void 0:n.signin)||"/":this.config.successAuthPath||(null===(o=this.authUrls)||void 0===o?void 0:o.successful)||"/",setTimeout((function(){window.location.href=l}),2e3));break;case 400:d=c.error,this.renderVerificationStatus("error","".concat(d.message," (").concat(d.code,")"));break;case 404:u=c.error,this.renderVerificationStatus("error","".concat(u.message," (").concat(u.code,")"));break;case 500:h=c.error,this.renderVerificationStatus("error","".concat(h.message," (").concat(h.code,")"));break;default:this.renderVerificationStatus("error","An unexpected error occurred. Please try again.")}return[3,5];case 4:return p=a.sent(),console.error("Error during email verification:",p),this.renderVerificationStatus("error","Network error: Unable to connect to the server. Please check your connection and try again."),[3,5];case 5:return[2]}}))}))},t.prototype.showMessage=function(t,e,n){var o,i,a,r=document.createElement("div");r.classList.add("authiqa-message"),r.classList.add("authiqa-message-".concat(e)),"success"===e?r.style.backgroundColor="#4caf50":"error"===e?r.style.backgroundColor="#f44336":"warning"===e&&(r.style.backgroundColor="#ff9800");var s=n;if("success"===e&&!n)switch(this.currentAction){case"signin":s=this.config.successAuthPath||(null===(o=this.authUrls)||void 0===o?void 0:o.successful);break;case"update":s=this.config.signinAuthPath||(null===(i=this.authUrls)||void 0===i?void 0:i.signin);break;case"signup":s=this.config.successAuthPath||(null===(a=this.authUrls)||void 0===a?void 0:a.successful)}var c=document.querySelector(".authiqa-message");c&&c.remove(),r.textContent=t,document.body.appendChild(r),r.classList.add("show");var l=2e3;"error"===e?l=7e3:"success"===e?l=4e3:"warning"===e&&(l=5e3),setTimeout((function(){r.classList.remove("show"),setTimeout((function(){r.remove(),s&&(window.location.href=s)}),300)}),l)},t.prototype.getCustomSuccessMessage=function(t){var e,n,o,i,a;switch(this.currentAction){case"signin":return(null===(e=this.config.messages)||void 0===e?void 0:e.signinSuccess)||t;case"signup":return(null===(n=this.config.messages)||void 0===n?void 0:n.signupSuccess)||"Successfully signed up! Welcome to your account.";case"reset":return(null===(o=this.config.messages)||void 0===o?void 0:o.resetSuccess)||t;case"update":return(null===(i=this.config.messages)||void 0===i?void 0:i.updateSuccess)||t;case"resend":return(null===(a=this.config.messages)||void 0===a?void 0:a.resendSuccess)||t;default:return t}},t.prototype.setLoadingState=function(t,e,n){if(e){var o=t.textContent||"Submit";t.setAttribute("data-original-text",o);var i=this.getCustomLoadingMessage(n)||"Please wait...";t.textContent=i}else t.textContent=t.getAttribute("data-original-text")||"Submit"},t.prototype.getCustomLoadingMessage=function(t){var e,n,o,i,a;switch(t){case"signin":return null===(e=this.config.messages)||void 0===e?void 0:e.signinLoading;case"signup":return null===(n=this.config.messages)||void 0===n?void 0:n.signupLoading;case"reset":return null===(o=this.config.messages)||void 0===o?void 0:o.resetLoading;case"update":return null===(i=this.config.messages)||void 0===i?void 0:i.updateLoading;case"resend":return null===(a=this.config.messages)||void 0===a?void 0:a.resendLoading;default:return}},t.prototype.injectStyles=function(){if(!this.config.disableStyles){var t=document.getElementById("authiqa-styles");t&&t.remove();var e=document.createElement("style");e.id="authiqa-styles";var n=this.config.theme&&"none"!==this.config.theme?this.config.theme:"light",o="";o+=(0,c.getStyleContent)(n);var i=(0,c.getComponentStyles)(n);o+="\n            /* Modal Styles */\n            .authiqa-modal-overlay {\n                ".concat(Object.entries(i.modal.overlay).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-modal-container {\n                ").concat(Object.entries(i.modal.container).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-iframe {\n                ").concat(Object.entries(i.iframe).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            /* Message Styles */\n            .authiqa-message {\n                ").concat(Object.entries(i.message).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-success {\n                ").concat(Object.entries(i.messageSuccess).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-error {\n                ").concat(Object.entries(i.messageError).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n            .authiqa-message-show {\n                ").concat(Object.entries(i.messageShow).map((function(t){var e=t[0],n=t[1];return"".concat(e,": ").concat(n,";")})).join("\n"),"\n            }\n        "),this.config.customization&&(o+=new l.StyleGenerator(this.config.customization).generateStyles(),o+=(0,c.generateTermsContainerStyles)(this.config)),e.textContent=o,document.head.appendChild(e)}},t.prototype.generateCustomStyles=function(t){var e=t.colors,n=t.typography,o=t.layout,i=t.buttons;return"\n            .authiqa-container {\n                background-color: ".concat(e.background,";\n                padding: ").concat(o.padding,";\n                margin: ").concat(o.margin,";\n                border-radius: ").concat(o.borderRadius,";\n                max-width: ").concat(o.maxWidth,";\n                font-family: ").concat(n.fontFamily,";\n            }\n\n            .authiqa-container h1 {\n                color: ").concat(n.titleColor,";\n                font-size: ").concat(n.titleSize,";\n            }\n\n            .authiqa-container input {\n                background-color: ").concat(e.inputBackground,";\n                color: ").concat(e.inputText,";\n                border: 1px solid ").concat(e.borderColor,";\n            }\n\n            .authiqa-container button {\n                background-color: ").concat(e.buttonBackground,";\n                color: ").concat(e.buttonText,";\n                height: ").concat(i.height||"40px",";\n                width: ").concat(i.width||"100%",";\n                border-radius: ").concat(i.borderRadius,";\n            }\n        ")},t.prototype.updateTheme=function(t){if(!this.config.disableStyles)if(document.getElementById("authiqa-styles")){"dark"===t?document.body.setAttribute("data-theme","dark"):document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&e.setAttribute("data-theme",t)}else this.injectStyles()},t.prototype.hasGoogleOneTapSuccessfulAuth=function(){try{return"true"===sessionStorage.getItem("authiqa_google_onetap_success")}catch(t){return!0===window._authiqaGoogleOneTapSuccessfulAuth}},t.prototype.markGoogleOneTapSuccessful=function(){try{sessionStorage.setItem("authiqa_google_onetap_success","true")}catch(t){window._authiqaGoogleOneTapSuccessfulAuth=!0}},t.prototype.dismissGoogleOneTap=function(){try{window.google&&window.google.accounts&&window.google.accounts.id&&window.google.accounts.id.cancel(),window._authiqaGoogleOneTapDismissed=!0,this.markGoogleOneTapSuccessful()}catch(t){}},t.prototype.resetGoogleOneTapState=function(){window._authiqaGoogleOneTapDismissed=!1,window._authiqaGoogleOneTapSuccessfulAuth=!1;try{sessionStorage.removeItem("authiqa_google_onetap_success")}catch(t){}},t.prototype.resetGoogleOneTap=function(){this.resetGoogleOneTapState()},t.prototype.cleanup=function(){this.dismissGoogleOneTap();var t=document.getElementById("authiqa-styles");t&&t.remove(),document.body.style.backgroundColor="",document.body.style.display="",document.body.style.minHeight="",document.body.style.alignItems="",document.body.style.justifyContent="",document.body.removeAttribute("data-theme");var e=document.getElementById(this.config.container);e&&(e.removeAttribute("data-theme"),e.style.marginTop="",e.style.marginBottom="",e.style.marginLeft="",e.style.marginRight="")},t.prototype.handleApiError=function(t){var e;(null===(e=null==t?void 0:t.error)||void 0===e?void 0:e.message)?this.showMessage(t.error.message,"error"):t instanceof Error?this.showMessage("Unable to connect to the server","error"):this.showMessage("An unexpected error occurred","error")},t.prototype.validateDomain=function(t){if(this.isDevelopmentMode())return!0;var e;try{e=new URL(t).hostname}catch(e){return console.error("Invalid organization URL:",t),!1}var n=window.location.hostname;return n===e||n.endsWith("."+e)||"authiqa.com"===n||"www.authiqa.com"===n},t.prototype.isDevelopmentMode=function(){var t=document.querySelector("script[data-public-key]");return!!t&&"true"===t.getAttribute("authiqa--dev-data-mode")},t.prototype.showUnauthorizedError=function(){var t=document.getElementById(this.config.container);if(t){t.innerHTML="";var e=document.createElement("div");e.className="authiqa-error-container";var n=document.createElement("h2");n.textContent="Unauthorized Domain",n.style.color="#e74c3c";var o=document.createElement("p");o.textContent="This widget can only be used on authorized domains. Please visit Authiqa and signin to update your organization related information",o.style.color="#333333";var i=document.createElement("a");i.href="https://authiqa.com",i.textContent="Visit Authiqa",i.style.color="#3498db",e.appendChild(n),e.appendChild(o),e.appendChild(i),t.appendChild(e);var a=document.createElement("style");a.textContent='\n            .authiqa-error-container {\n                padding: 20px;\n                border: 1px solid #e74c3c;\n                border-radius: 5px;\n                background-color: #fef5f5;\n                text-align: center;\n                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;\n            }\n            .authiqa-error-container h2 {\n                color: #e74c3c;\n                margin-top: 0;\n            }\n            .authiqa-error-container p {\n                color: #333333;\n                margin-bottom: 15px;\n            }\n            .authiqa-error-container a {\n                display: inline-block;\n                margin-top: 15px;\n                color: #3498db;\n                text-decoration: none;\n            }\n            .authiqa-error-container a:hover {\n                text-decoration: underline;\n            }\n        ',document.head.appendChild(a)}},t.prototype.shouldEnableGoogleOneTap=function(){return!1!==this.config.enableGoogleOneTap&&("signin"===this.currentAction||"signup"===this.currentAction)},t}();e.AuthiqaWidget=d,window.AuthiqaWidget=d,function(){var t=new URLSearchParams(window.location.search),e=t.get("code"),n=t.get("state"),o=sessionStorage.getItem("github_oauth_state");if(e&&n&&o){var i=document.createElement("div");i.id="github-callback-overlay",i.style.cssText="\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100%;\n            height: 100%;\n            background: rgba(0, 0, 0, 0.8);\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            z-index: 10000;\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        ";var a=document.createElement("div");a.style.cssText="\n            background: white;\n            padding: 2rem;\n            border-radius: 8px;\n            text-align: center;\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n        ";var r=document.createElement("div");r.textContent="Processing GitHub authentication...",r.style.cssText="\n            color: #333;\n            font-size: 16px;\n            margin-bottom: 1rem;\n        ";var s=document.createElement("div");s.style.cssText="\n            width: 40px;\n            height: 40px;\n            border: 4px solid #f3f3f3;\n            border-top: 4px solid #4CAF50;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin: 0 auto;\n        ";var c=document.createElement("style");c.textContent="\n            @keyframes spin {\n                0% { transform: rotate(0deg); }\n                100% { transform: rotate(360deg); }\n            }\n        ",document.head.appendChild(c),a.appendChild(r),a.appendChild(s),i.appendChild(a),document.body?document.body.appendChild(i):document.addEventListener("DOMContentLoaded",(function(){document.body.appendChild(i)}))}}(),document.addEventListener("DOMContentLoaded",(function(){if(window.authiqaInitialized)console.log("🚫 Authiqa already initialized, skipping...");else{window.authiqaInitialized=!0,console.log("🎯 Starting Authiqa initialization...");try{var t=document.querySelector("script[data-public-key]");if(!t)return void console.error("Script tag with data-public-key not found.");var e=t.getAttribute("data-public-key"),n=t.getAttribute("action");if(!n){var o=window.location.href;n=["signin","signup","verify","reset","update","resend"].find((function(t){return o.includes(t)}))||"signin"}var i=t.getAttribute("termsAndConditions"),a=t.getAttribute("privacy"),r=t.getAttribute("notificationSettings"),s=t.getAttribute("theme")||"light",c=t.getAttribute("preset");console.log("🎨 Preset detected:",c);var l="true"===t.getAttribute("disable-styles"),d=t.getAttribute("verifyAuthPath"),u=t.getAttribute("updatePasswordPath"),h=t.getAttribute("resendAuthPath"),p=t.getAttribute("resetAuthPath"),m=t.getAttribute("successAuthPath"),g=t.getAttribute("signinAuthPath"),f=t.getAttribute("signupAuthPath"),v=void 0,b=t.getAttribute("data-messages");if(b)try{v=JSON.parse(b)}catch(t){console.error("Failed to parse custom messages:",t)}var y=void 0,w=t.getAttribute("data-customization");if(w)try{y=JSON.parse(w)}catch(t){console.error("Failed to parse customization:",t)}var x="false"!==t.getAttribute("enable-google-one-tap");if("function"!=typeof window.AuthiqaWidget)return void console.error("AuthiqaWidget not properly registered");var T={publicKey:e||"",container:"authiqa",mode:"popup",theme:s,preset:c||void 0,disableStyles:l,organizationDomain:"authiqa.com",enableGoogleOneTap:x,termsAndConditions:i,privacy:a,notificationSettings:r,messages:v,customization:y,verifyAuthPath:d,updatePasswordPath:u,resendAuthPath:h,resetAuthPath:p,successAuthPath:m,signinAuthPath:g,signupAuthPath:f};console.log("🚀 Creating widget with config:",{preset:T.preset,action:n});var S=new window.AuthiqaWidget(T);console.log("📱 Showing widget with action:",n),S.show(n)}catch(t){console.error("Error during widget initialization:",t)}}}))},752:function(t,e,n){var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)},i=this&&this.__awaiter||function(t,e,n,o){return new(n||(n=Promise))((function(i,a){function r(t){try{c(o.next(t))}catch(t){a(t)}}function s(t){try{c(o.throw(t))}catch(t){a(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,s)}c((o=o.apply(t,e||[])).next())}))},a=this&&this.__generator||function(t,e){var n,o,i,a,r={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,o&&(i=2&s[0]?o.return:s[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,s[1])).done)return i;switch(o=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,o=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!((i=(i=r.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){r.label=s[1];break}if(6===s[0]&&r.label<i[1]){r.label=i[1],i=s;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(s);break}i[2]&&r.ops.pop(),r.trys.pop();continue}s=e.call(t,r)}catch(t){s=[6,t],o=0}finally{n=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}};Object.defineProperty(e,"__esModule",{value:!0}),e.ApiService=void 0;var r=n(616),s=n(733),c=function(){function t(t){this.publicKey=t.publicKey,this.config=(0,r.getApiConfig)(t.organizationDomain),(0,s.validateCustomAuthPaths)({verifyAuthPath:t.verifyAuthPath,updatePasswordPath:t.updatePasswordPath,resendAuthPath:t.resendAuthPath,successAuthPath:t.successAuthPath,signinAuthPath:t.signinAuthPath},t.organizationDomain).isValid&&(this.verifyAuthPath=t.verifyAuthPath,this.updatePasswordPath=t.updatePasswordPath,this.resendAuthPath=t.resendAuthPath,this.successAuthPath=t.successAuthPath,this.signinAuthPath=t.signinAuthPath)}return t.prototype.signup=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=o(o({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resetPassword=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=o(o({},t),{updatePasswordPath:this.updatePasswordPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/reset-password"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.resendConfirmation=function(t){return i(this,void 0,void 0,(function(){var e;return a(this,(function(n){return e=o(o({},t),{verifyAuthPath:this.verifyAuthPath}),[2,fetch("".concat(this.config.API_BASE,"/auth/request-new-confirmation"),{method:"POST",headers:{"Content-Type":"application/json","X-Public-Key":this.publicKey},body:JSON.stringify(e)})]}))}))},t.prototype.getApiBase=function(){return this.config.API_BASE},t.prototype.getOrganizationDetails=function(){return i(this,void 0,void 0,(function(){var t,e,n,o,i,r;return a(this,(function(a){switch(a.label){case 0:t="".concat(this.config.API_BASE,"/auth/organization-details"),a.label=1;case 1:return a.trys.push([1,4,,5]),[4,fetch(t,{method:"GET",headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 2:if(!(e=a.sent()).ok)throw new Error("API Error: ".concat(e.statusText));return i=(o=JSON).parse,[4,e.text()];case 3:if((n=i.apply(o,[a.sent()])).success&&n.data)return[2,n.data];throw new Error("Invalid response format from server");case 4:throw r=a.sent(),console.error("Organization Details Request Failed",r),r;case 5:return[2]}}))}))},t.prototype.checkAuthStatus=function(){return i(this,void 0,void 0,(function(){var t;return a(this,(function(e){switch(e.label){case 0:return[4,fetch("".concat(this.config.API_BASE).concat(this.config.ENDPOINTS.AUTH_STATUS),{headers:{"X-Public-Key":this.publicKey,"Content-Type":"application/json"}})];case 1:if(!(t=e.sent()).ok)throw new Error("API Error: ".concat(t.statusText));return[2,t.json()]}}))}))},t}();e.ApiService=c},616:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getApiConfig=void 0,e.getApiConfig=function(t){var e;return{API_BASE:"staging"==(((null==(e=document.querySelector("script[data-public-key]"))?void 0:e.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.".concat(t),ENDPOINTS:{ORGANIZATION_DETAILS:"/auth/organization-details",AUTH_STATUS:"/auth/status"}}}},149:(t,e)=>{function n(){var t;return"staging"==(((null==(t=document.querySelector("script[data-public-key]"))?void 0:t.getAttribute("src"))||"").includes("staging.widget.authiqa.com")?"staging":"production")?"https://staging.api.authiqa.com":"https://api.authiqa.com"}Object.defineProperty(e,"__esModule",{value:!0}),e.STYLE_CONSTANTS=e.THEMES=e.API_ENDPOINTS=void 0,e.API_ENDPOINTS={ORGANIZATION_DETAILS:"".concat(n(),"/auth/organization-details"),AUTH_STATUS:"".concat(n(),"/auth/status")},e.THEMES={light:{background:"#ffffff",text:"#000000",border:"#e0e0e0",modalOverlay:"rgba(0, 0, 0, 0.5)",labelColor:"#333333"},dark:{background:"#1a1a1a",text:"#ffffff",border:"#333333",modalOverlay:"rgba(0, 0, 0, 0.7)",labelColor:"#ffffff"}},e.STYLE_CONSTANTS={STYLE_ELEMENT_ID:"authiqa-styles",CONTAINER_CLASS:"authiqa-container",THEMES:{LIGHT:"light",DARK:"dark"}}},745:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.defaultCustomization=void 0,e.defaultCustomization={layout:{padding:"1.5rem",paddingTop:"1.25rem",margin:"2rem",borderRadius:"16px",maxWidth:"400px",minWidth:"300px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#000000",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#000000",borderColor:"#e5e5e5"},typography:{titleText:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"2rem",titleColor:"#1a1a1a",labelSize:"0.9rem",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',labelColor:"#333333",labelFontWeight:"400",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.2",termsText:{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#333333",linkColor:"#000000"},navTextColor:"#1a1a1a",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Email Address",passwordPlaceholder:"Password",usernamePlaceholder:"Username",confirmPasswordPlaceholder:"Confirm Password",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm Password",borderRadius:"4px",height:"50px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#000000",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Create Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation",height:"40px",width:"100%",borderRadius:"4px",hoverBackground:"#27272a"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign In",signupPrompt:"Don't have an account?",signupLinkText:"Sign Up",forgotPrompt:"Forgot Password?",forgotLinkText:"Reset",fontSize:"0.95rem",color:"#1a1a1a",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#0070f3",linkFontWeight:"500",backToSigninPrompt:"Back to Sign In?"}}},189:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.sharpPreset=e.roundedPreset=e.gradientPreset=void 0,e.gradientPreset={name:"gradient",displayName:"Gradient",description:"Modern gradient design with vibrant colors and smooth transitions",category:"creative",customization:{layout:{padding:"2rem",paddingTop:"1.5rem",margin:"1rem",borderRadius:"16px",maxWidth:"400px",minWidth:"320px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",buttonBackground:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",buttonText:"#ffffff",inputBackground:"rgba(255, 255, 255, 0.9)",inputText:"#2d3748",inputPlaceholder:"#718096",borderColor:"rgba(255, 255, 255, 0.3)"},typography:{titleText:{signinText:"Welcome Back",signupText:"Join Us",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Email"},subtitleText:{signinText:"Sign in to continue your journey",signupText:"Start your amazing journey",resetText:"We'll help you reset it",updateText:"Choose a new secure password",verifyText:"Check your email for verification",resendText:"Get a new verification email"},titleSize:"2rem",titleColor:"#ffffff",labelSize:"0.875rem",fontFamily:'"Poppins", -apple-system, BlinkMacSystemFont, sans-serif',labelColor:"#ffffff",labelFontWeight:"500",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.2",termsText:{agreePrefix:"I agree to the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"rgba(255, 255, 255, 0.8)",linkColor:"#ffffff"},navTextColor:"rgba(255, 255, 255, 0.9)",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Your email address",passwordPlaceholder:"Your password",usernamePlaceholder:"Choose username",confirmPasswordPlaceholder:"Confirm password",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm Password",borderRadius:"12px",height:"50px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#ffffff",focusBoxShadow:"0 0 0 3px rgba(255, 255, 255, 0.3)",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Get Started",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Email",height:"50px",width:"100%",borderRadius:"12px",hoverBackground:"linear-gradient(135deg, #f5576c 0%, #f093fb 100%)"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign in",signupPrompt:"New here?",signupLinkText:"Create account",forgotPrompt:"Forgot password?",forgotLinkText:"Reset it",fontSize:"0.875rem",color:"rgba(255, 255, 255, 0.9)",fontFamily:'"Poppins", -apple-system, BlinkMacSystemFont, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#ffffff",linkFontWeight:"600",backToSigninPrompt:"Back to sign in"}},preview:{primaryColor:"#f093fb",backgroundColor:"#667eea",borderRadius:"16px",fontFamily:"Poppins"}},e.roundedPreset={name:"rounded",displayName:"Rounded",description:"Friendly design with rounded corners and soft, approachable styling",category:"modern",customization:{layout:{padding:"2rem",paddingTop:"1.5rem",margin:"1.5rem",borderRadius:"24px",maxWidth:"400px",minWidth:"320px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#10b981",buttonText:"#ffffff",inputBackground:"#f9fafb",inputText:"#111827",inputPlaceholder:"#9ca3af",borderColor:"#e5e7eb"},typography:{titleText:{signinText:"Hello Again!",signupText:"Welcome!",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Email"},subtitleText:{signinText:"We're happy to see you back",signupText:"Let's get you started",resetText:"Don't worry, it happens to everyone",updateText:"Choose a strong new password",verifyText:"Please check your email",resendText:"We'll send another email"},titleSize:"1.875rem",titleColor:"#1f2937",labelSize:"0.875rem",fontFamily:'"Nunito", -apple-system, BlinkMacSystemFont, sans-serif',labelColor:"#374151",labelFontWeight:"600",titleAlignment:"center",titleWeight:"700",titleLineHeight:"1.25",termsText:{agreePrefix:"I agree to the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#6b7280",linkColor:"#10b981"},navTextColor:"#6b7280",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Enter your email",passwordPlaceholder:"Enter your password",usernamePlaceholder:"Choose a username",confirmPasswordPlaceholder:"Confirm your password",emailLabel:"Email Address",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm Password",borderRadius:"16px",height:"52px",width:"100%",padding:"0 1.25rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#10b981",focusBoxShadow:"0 0 0 3px rgba(16, 185, 129, 0.1)",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Get Started",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Email",height:"52px",width:"100%",borderRadius:"16px",hoverBackground:"#059669"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign in here",signupPrompt:"New to our platform?",signupLinkText:"Create account",forgotPrompt:"Forgot your password?",forgotLinkText:"Reset it here",fontSize:"0.875rem",color:"#6b7280",fontFamily:'"Nunito", -apple-system, BlinkMacSystemFont, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"500",linkColor:"#10b981",linkFontWeight:"600",backToSigninPrompt:"Back to sign in"}},preview:{primaryColor:"#10b981",backgroundColor:"#ffffff",borderRadius:"24px",fontFamily:"Nunito"}},e.sharpPreset={name:"sharp",displayName:"Sharp",description:"Bold, angular design with sharp edges and high contrast",category:"modern",customization:{layout:{padding:"2rem",paddingTop:"1.5rem",margin:"1rem",borderRadius:"0px",maxWidth:"400px",minWidth:"320px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#dc2626",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#111827",inputPlaceholder:"#6b7280",borderColor:"#374151"},typography:{titleText:{signinText:"ACCESS",signupText:"REGISTER",resetText:"RESET",updateText:"UPDATE",verifyText:"VERIFY",resendText:"RESEND"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"2.25rem",titleColor:"#111827",labelSize:"0.75rem",fontFamily:'"Roboto Condensed", "Arial Narrow", Arial, sans-serif',labelColor:"#374151",labelFontWeight:"700",titleAlignment:"center",titleWeight:"900",titleLineHeight:"1",termsText:{agreePrefix:"I ACCEPT THE",andConnector:"&",defaultPrefix:"STANDARD",linkText:{terms:"TERMS",privacy:"PRIVACY",notifications:"NOTIFICATIONS"},textColor:"#6b7280",linkColor:"#dc2626"},navTextColor:"#6b7280",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"EMAIL",passwordPlaceholder:"PASSWORD",usernamePlaceholder:"USERNAME",confirmPasswordPlaceholder:"CONFIRM",emailLabel:"EMAIL",passwordLabel:"PASSWORD",usernameLabel:"USERNAME",confirmPasswordLabel:"CONFIRM",borderRadius:"0px",height:"48px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"500",focusBorderColor:"#dc2626",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"ACCESS",signupText:"REGISTER",resetText:"RESET",updateText:"UPDATE",verifyText:"VERIFY",resendText:"RESEND",height:"48px",width:"100%",borderRadius:"0px",hoverBackground:"#b91c1c"},navLinks:{signinPrompt:"HAVE ACCOUNT?",signinLinkText:"ACCESS",signupPrompt:"NEED ACCOUNT?",signupLinkText:"REGISTER",forgotPrompt:"FORGOT?",forgotLinkText:"RESET",fontSize:"0.75rem",color:"#6b7280",fontFamily:'"Roboto Condensed", "Arial Narrow", Arial, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"700",linkColor:"#dc2626",linkFontWeight:"700",backToSigninPrompt:"BACK"}},preview:{primaryColor:"#dc2626",backgroundColor:"#ffffff",borderRadius:"0px",fontFamily:"Roboto Condensed"}}},903:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.corporatePreset=e.minimalPreset=e.classicPreset=e.modernPreset=void 0,e.modernPreset={name:"modern",displayName:"Modern",description:"Clean, contemporary design with subtle shadows and modern typography",category:"modern",customization:{layout:{padding:"2rem",paddingTop:"1.5rem",margin:"1rem",borderRadius:"12px",maxWidth:"420px",minWidth:"320px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#1f2937",buttonBackground:"#3b82f6",buttonText:"#ffffff",inputBackground:"#374151",inputText:"#ffffff",inputPlaceholder:"#9ca3af",borderColor:"#4b5563"},typography:{titleText:{signinText:"Welcome Back",signupText:"Create an account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"1.5rem",titleColor:"#ffffff",labelSize:"0.875rem",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',labelColor:"#ffffff",labelFontWeight:"500",titleAlignment:"left",titleWeight:"600",titleLineHeight:"1.25",termsText:{agreePrefix:"I agree to the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#d1d5db",linkColor:"#3b82f6"},navTextColor:"#d1d5db",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"",passwordPlaceholder:"",usernamePlaceholder:"",confirmPasswordPlaceholder:"",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm password",borderRadius:"6px",height:"48px",width:"100%",padding:"0 1rem",margin:"0 0 1.5rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#3b82f6",focusBoxShadow:"0 0 0 2px rgba(59, 130, 246, 0.3)",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Sign Up",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Email",height:"48px",width:"100%",borderRadius:"6px",hoverBackground:"#2563eb"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign in",signupPrompt:"Don't have an account?",signupLinkText:"Sign up",forgotPrompt:"Forgot your password?",forgotLinkText:"Reset it",fontSize:"0.875rem",color:"#d1d5db",fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#3b82f6",linkFontWeight:"500",backToSigninPrompt:"Back to sign in"},passwordToggle:{position:"right",rightOffset:"12px",color:"#9ca3af",hoverColor:"#ffffff",size:"14px",padding:"2px"}},preview:{primaryColor:"#3b82f6",backgroundColor:"#1f2937",borderRadius:"12px",fontFamily:"System"}},e.classicPreset={name:"classic",displayName:"Classic",description:"Facebook-inspired clean design with blue primary button and rounded inputs",category:"traditional",customization:{layout:{padding:"2rem",paddingTop:"1.5rem",margin:"1rem",borderRadius:"8px",maxWidth:"396px",minWidth:"320px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#1877f2",buttonText:"#ffffff",inputBackground:"#f5f6f7",inputText:"#1c1e21",inputPlaceholder:"#8a8d91",borderColor:"#dddfe2"},typography:{titleText:{signinText:"Log in",signupText:"Create new account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"1.5rem",titleColor:"#1c1e21",labelSize:"0.875rem",fontFamily:"Helvetica, Arial, sans-serif",labelColor:"#1c1e21",labelFontWeight:"400",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.2",termsText:{agreePrefix:"I agree with the",andConnector:"and",defaultPrefix:"default",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Notification Settings"},textColor:"#65676b",linkColor:"#1877f2"},navTextColor:"#1877f2",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Email address or phone number",passwordPlaceholder:"Password",usernamePlaceholder:"Username",confirmPasswordPlaceholder:"Confirm Password",emailLabel:"",passwordLabel:"",usernameLabel:"",confirmPasswordLabel:"",borderRadius:"6px",height:"52px",width:"100%",padding:"0 1rem",margin:"0 0 1rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#1877f2",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"Log in",signupText:"Create new account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Email",resendText:"Resend Confirmation",height:"48px",width:"100%",borderRadius:"6px",hoverBackground:"#166fe5"},navLinks:{signinPrompt:"Already have an account?",signinLinkText:"Sign In",signupPrompt:"",signupLinkText:"Create new account",forgotPrompt:"",forgotLinkText:"Forgotten password?",fontSize:"0.875rem",color:"#1877f2",fontFamily:"Helvetica, Arial, sans-serif",textAlign:"center",marginTop:"1rem",marginBottom:"1rem",fontWeight:"400",linkColor:"#1877f2",linkFontWeight:"400",backToSigninPrompt:"Back to Sign In",secondaryButtonStyle:!0,secondaryButtonBackground:"#42b883",secondaryButtonText:"#ffffff",secondaryButtonHover:"#369870",secondaryButtonBorderRadius:"6px",secondaryButtonHeight:"48px",secondaryButtonPadding:"0.75rem 1.5rem"},passwordToggle:{position:"right",rightOffset:"12px",color:"#8a8d91",hoverColor:"#1c1e21",size:"14px",padding:"2px"}},preview:{primaryColor:"#1877f2",backgroundColor:"#ffffff",borderRadius:"8px",fontFamily:"Helvetica"}},e.minimalPreset={name:"minimal",displayName:"Minimal",description:"Ultra-clean design with maximum whitespace and minimal visual elements",category:"modern",customization:{layout:{padding:"3rem",paddingTop:"2rem",margin:"1rem",borderRadius:"0px",maxWidth:"380px",minWidth:"300px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#ffffff",buttonBackground:"#000000",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#000000",inputPlaceholder:"#a3a3a3",borderColor:"#e5e5e5"},typography:{titleText:{signinText:"Sign In",signupText:"Sign Up",resetText:"Reset",updateText:"Update",verifyText:"Verify",resendText:"Resend"},subtitleText:{signinText:"",signupText:"",resetText:"",updateText:"",verifyText:"",resendText:""},titleSize:"1.5rem",titleColor:"#000000",labelSize:"0.75rem",fontFamily:"Helvetica Neue, Helvetica, Arial, sans-serif",labelColor:"#666666",labelFontWeight:"300",titleAlignment:"left",titleWeight:"300",titleLineHeight:"1.1",termsText:{agreePrefix:"I agree to the",andConnector:"&",defaultPrefix:"default",linkText:{terms:"Terms",privacy:"Privacy",notifications:"Notifications"},textColor:"#999999",linkColor:"#000000"},navTextColor:"#666666",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"email",passwordPlaceholder:"password",usernamePlaceholder:"username",confirmPasswordPlaceholder:"confirm password",emailLabel:"Email",passwordLabel:"Password",usernameLabel:"Username",confirmPasswordLabel:"Confirm",borderRadius:"0px",height:"50px",width:"100%",padding:"0 0",margin:"0 0 2rem 0",fontSize:"1rem",fontWeight:"300",focusBorderColor:"#000000",focusBoxShadow:"none",placeholderAlign:"left"},buttons:{signinText:"Sign In",signupText:"Sign Up",resetText:"Reset",updateText:"Update",verifyText:"Verify",resendText:"Resend",height:"50px",width:"100%",borderRadius:"0px",hoverBackground:"#333333"},navLinks:{signinPrompt:"Have an account?",signinLinkText:"Sign in",signupPrompt:"Need an account?",signupLinkText:"Sign up",forgotPrompt:"Forgot password?",forgotLinkText:"Reset",fontSize:"0.75rem",color:"#999999",fontFamily:"Helvetica Neue, Helvetica, Arial, sans-serif",textAlign:"left",marginTop:"2rem",marginBottom:"0",fontWeight:"300",linkColor:"#000000",linkFontWeight:"300",backToSigninPrompt:"Back"}},preview:{primaryColor:"#000000",backgroundColor:"#ffffff",borderRadius:"0px",fontFamily:"Helvetica Neue"}},e.corporatePreset={name:"corporate",displayName:"Corporate",description:"Professional business design with corporate colors and formal typography",category:"professional",customization:{layout:{padding:"2.5rem",paddingTop:"2rem",margin:"2rem",borderRadius:"8px",maxWidth:"450px",minWidth:"350px",width:"auto",height:"auto",minHeight:"auto",maxHeight:"auto"},colors:{background:"#f8f9fa",buttonBackground:"#0d47a1",buttonText:"#ffffff",inputBackground:"#ffffff",inputText:"#212529",inputPlaceholder:"#6c757d",borderColor:"#ced4da"},typography:{titleText:{signinText:"Employee Portal",signupText:"Register Account",resetText:"Password Recovery",updateText:"Update Credentials",verifyText:"Account Verification",resendText:"Resend Verification"},subtitleText:{signinText:"Access your corporate account",signupText:"Create your employee account",resetText:"Recover your account access",updateText:"Update your login credentials",verifyText:"Verify your email address",resendText:"Request new verification email"},titleSize:"1.75rem",titleColor:"#0d47a1",labelSize:"0.875rem",fontFamily:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',labelColor:"#495057",labelFontWeight:"500",titleAlignment:"center",titleWeight:"600",titleLineHeight:"1.3",termsText:{agreePrefix:"I acknowledge and agree to the",andConnector:"and",defaultPrefix:"company",linkText:{terms:"Terms of Service",privacy:"Privacy Policy",notifications:"Communication Preferences"},textColor:"#6c757d",linkColor:"#0d47a1"},navTextColor:"#495057",navTextColorDark:"#ffffff"},inputs:{emailPlaceholder:"Corporate Email Address",passwordPlaceholder:"Secure Password",usernamePlaceholder:"Employee ID or Username",confirmPasswordPlaceholder:"Confirm New Password",emailLabel:"Email Address",passwordLabel:"Password",usernameLabel:"Employee ID",confirmPasswordLabel:"Confirm Password",borderRadius:"6px",height:"46px",width:"100%",padding:"0 1rem",margin:"0 0 1.25rem 0",fontSize:"1rem",fontWeight:"400",focusBorderColor:"#0d47a1",focusBoxShadow:"0 0 0 2px rgba(13, 71, 161, 0.25)",placeholderAlign:"left"},buttons:{signinText:"Access Portal",signupText:"Register Account",resetText:"Reset Password",updateText:"Update Password",verifyText:"Verify Account",resendText:"Resend Verification",height:"46px",width:"100%",borderRadius:"6px",hoverBackground:"#1565c0"},navLinks:{signinPrompt:"Already registered?",signinLinkText:"Sign in here",signupPrompt:"Need an account?",signupLinkText:"Register now",forgotPrompt:"Forgot your password?",forgotLinkText:"Reset password",fontSize:"0.875rem",color:"#6c757d",fontFamily:'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',textAlign:"center",marginTop:"1.5rem",marginBottom:"0",fontWeight:"400",linkColor:"#0d47a1",linkFontWeight:"500",backToSigninPrompt:"Return to sign in"}},preview:{primaryColor:"#0d47a1",backgroundColor:"#f8f9fa",borderRadius:"8px",fontFamily:"Segoe UI"}}},395:function(t,e,n){var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)},i=this&&this.__rest||function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(o=Object.getOwnPropertySymbols(t);i<o.length;i++)e.indexOf(o[i])<0&&Object.prototype.propertyIsEnumerable.call(t,o[i])&&(n[o[i]]=t[o[i]])}return n};Object.defineProperty(e,"__esModule",{value:!0}),e.presetManager=e.PresetManager=void 0;var a=n(903),r=n(189);function s(t,e){var n=o({},t);for(var i in e)void 0!==e[i]&&null!==e[i]&&("object"!=typeof e[i]||Array.isArray(e[i])?n[i]=e[i]:n[i]=s(n[i]||{},e[i]));return n}var c=function(){function t(){this.presets=new Map,this.initializePresets()}return t.prototype.initializePresets=function(){var t=this;[a.modernPreset,a.classicPreset,a.minimalPreset,a.corporatePreset,r.gradientPreset,r.roundedPreset,r.sharpPreset].forEach((function(e){t.presets.set(e.name,e)}))},t.prototype.getPreset=function(t){return this.presets.get(t)||null},t.prototype.getAllPresets=function(){return Array.from(this.presets.values())},t.prototype.getPresetsByCategory=function(t){return this.getAllPresets().filter((function(e){return e.category===t}))},t.prototype.mergePresetWithCustomization=function(t,e){return e?s(t.customization,e):t.customization},t.prototype.applyPreset=function(t,e){var n=this.getPreset(t);if(!n)throw new Error('Preset "'.concat(t,'" not found'));return this.mergePresetWithCustomization(n,e)},t.prototype.getCategories=function(){var t=new Set;return this.getAllPresets().forEach((function(e){t.add(e.category)})),Array.from(t)},t.prototype.hasPreset=function(t){return this.presets.has(t)},t.prototype.getPresetPreview=function(t){var e=this.getPreset(t);return(null==e?void 0:e.preview)||null},t.prototype.registerPreset=function(t){this.presets.set(t.name,t)},t.prototype.removePreset=function(t){return this.presets.delete(t)},t.prototype.getPresetMetadata=function(t){var e=this.getPreset(t);return e?(e.customization,i(e,["customization"])):null},t.prototype.searchPresets=function(t){var e=t.toLowerCase();return this.getAllPresets().filter((function(t){return t.name.toLowerCase().includes(e)||t.displayName.toLowerCase().includes(e)||t.description.toLowerCase().includes(e)}))},t}();e.PresetManager=c,e.presetManager=new c},113:function(t,e,n){var o=this&&this.__assign||function(){return o=Object.assign||function(t){for(var e,n=1,o=arguments.length;n<o;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.StyleGenerator=void 0;var i=n(745),a=function(){function t(t){var e,n;this.customization=o(o(o({},i.defaultCustomization),t),{typography:o(o(o({},i.defaultCustomization.typography),null==t?void 0:t.typography),{titleText:o(o({},i.defaultCustomization.typography.titleText),null===(e=null==t?void 0:t.typography)||void 0===e?void 0:e.titleText),subtitleText:o(o({},i.defaultCustomization.typography.subtitleText),null===(n=null==t?void 0:t.typography)||void 0===n?void 0:n.subtitleText)})})}return t.prototype.generateStyles=function(){var t,e,n,o,i,a,r,s,c,l,d,u,h,p,m,g,f,v,b,y,w,x,T,S,P,C,k,A,q,L,z,E,I,B,M,R,_,N,O,U,H,j,V,F,D,G,W,K,J,Y,X,Z,$,Q,tt,et,nt=this.customization,ot=nt.layout,it=nt.colors,at=nt.typography,rt=nt.buttons,st=it.background&&("#27272a"===it.background.toLowerCase()||"#18181b"===it.background.toLowerCase());return"\n        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap');\n        \n        .authiqa-container {\n            background-color: ".concat(it.background,";\n            padding: ").concat(ot.paddingTop||"1.25rem"," ").concat(ot.padding," ").concat(ot.padding," ").concat(ot.padding,";\n            margin: ").concat(ot.margin,";\n            border-radius: ").concat(ot.borderRadius,";\n            max-width: ").concat(ot.maxWidth,";\n            min-width: ").concat(ot.minWidth||"auto",";\n            width: ").concat(ot.width||"auto",";\n            height: ").concat(ot.height||"auto",";\n            min-height: ").concat(ot.minHeight||"auto",";\n            max-height: ").concat(ot.maxHeight||"auto",";\n            font-family: ").concat(at.fontFamily,";\n            --authiqa-nav-text-color: ").concat(at.navTextColor||"#1a1a1a",";\n            --authiqa-nav-text-color-dark: ").concat(at.navTextColorDark||"#ffffff",";\n        }\n\n        .authiqa-container h1 {\n            color: ").concat(at.titleColor,";\n            font-size: ").concat(at.titleSize,";\n            margin-top: 0;\n            margin-bottom: 2rem;\n            text-align: ").concat(at.titleAlignment||"center",";\n            font-weight: ").concat(at.titleWeight||"600",";\n            line-height: ").concat(at.titleLineHeight||"1.2",";\n        }\n\n        .authiqa-container input {\n            background-color: ").concat(it.inputBackground,";\n            color: ").concat(it.inputText,";\n            border: 1px solid ").concat(it.borderColor,";\n            border-radius: ").concat((null===(t=this.customization.inputs)||void 0===t?void 0:t.borderRadius)||"4px",";\n            height: ").concat((null===(e=this.customization.inputs)||void 0===e?void 0:e.height)||"50px",";\n            width: ").concat((null===(n=this.customization.inputs)||void 0===n?void 0:n.width)||"100%",";\n            padding: ").concat((null===(o=this.customization.inputs)||void 0===o?void 0:o.padding)||"0 1rem",";\n            margin: ").concat((null===(i=this.customization.inputs)||void 0===i?void 0:i.margin)||"0 0 1rem 0",";\n            font-size: ").concat((null===(a=this.customization.inputs)||void 0===a?void 0:a.fontSize)||"1rem",";\n            font-weight: ").concat((null===(r=this.customization.inputs)||void 0===r?void 0:r.fontWeight)||"400",";\n            box-sizing: border-box;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        .authiqa-container input:focus {\n            border-color: ").concat((null===(s=this.customization.inputs)||void 0===s?void 0:s.focusBorderColor)||"#000000",";\n            box-shadow: ").concat((null===(c=this.customization.inputs)||void 0===c?void 0:c.focusBoxShadow)||"none",';\n            outline: none;\n        }\n\n        /* Make button selectors more specific to override defaults, but exclude GitHub buttons and password toggle */\n        .authiqa-container button:not(.authiqa-github-button):not(.password-toggle),\n        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle),\n        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle) {\n            background-color: ').concat(it.buttonBackground," !important;\n            color: ").concat(it.buttonText," !important;\n            height: ").concat(rt.height||"40px"," !important;\n            width: ").concat(rt.width||"100%"," !important;\n            border-radius: ").concat(rt.borderRadius,' !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container button[type="submit"]:not(.authiqa-github-button):not(.password-toggle):hover,\n        .authiqa-container .authiqa-button:not(.authiqa-github-button):not(.password-toggle):hover {\n            background-color: ').concat(rt.hoverBackground||it.buttonBackground,' !important;\n        }\n\n        /* GitHub button specific styling with proper interactivity */\n        .authiqa-container .authiqa-github-button {\n            background-color: #ffffff !important;\n            color: #000000 !important;\n            border: 1px solid #30363d !important;\n            border-radius: 4px !important;\n            padding: 0.5rem 1rem 0.5rem 0.9rem !important;\n            font-family: \'Roboto\', \'Helvetica Neue\', Arial, sans-serif !important;\n            font-size: 14px !important;\n            font-weight: 500 !important;\n            width: 100% !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: flex-start !important;\n            gap: 0.5rem !important;\n            cursor: pointer !important;\n            transition: all 0.2s ease !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container .authiqa-github-button:hover {\n            background-color: #f6f8fa !important;\n            border-color: #1f2328 !important;\n            transform: translateY(-1px) !important;\n            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;\n        }\n        .authiqa-container .authiqa-github-button:active {\n            transform: scale(0.98) !important;\n            box-shadow: none !important;\n            background-color: #eaeef2 !important;\n        }\n        \n        /* Google button styling - clean and simple like GitHub */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"],\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"] {\n            background-color: #ffffff !important;\n            color: #000000 !important;\n            border: none !important;\n            border-radius: 4px !important;\n            padding: 0.5rem 1rem 0.5rem 0.1rem !important;\n            font-family: \'Roboto\', \'Helvetica Neue\', Arial, sans-serif !important;\n            font-size: 14px !important;\n            font-weight: 500 !important;\n            width: 100% !important;\n            max-width: 100% !important;\n            min-width: 100% !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: flex-start !important;\n            gap: 0.5rem !important;\n            cursor: pointer !important;\n            transition: none !important;\n            transform: none !important;\n            line-height: 1 !important;\n            box-sizing: border-box !important;\n            height: 40px !important;\n            margin: 0 !important;\n            outline: none !important;\n            box-shadow: none !important;\n        }\n        \n        /* Remove all hover and active effects */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:hover,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:active,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb:focus,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:hover,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:active,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe:focus,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:hover,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:active,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb"]:focus,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:hover,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:active,\n        .authiqa-container div[role="button"][data-idom-class="nsm7Bb-HzV7m-LgbsSe-MJoBVe"]:focus {\n            background-color: #ffffff !important;\n            transform: none !important;\n            box-shadow: none !important;\n            border: none !important;\n            outline: none !important;\n            transition: none !important;\n        }\n        \n        /* Google button logo positioning */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb .nsm7Bb-HzV7m-LgbsSe,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb svg,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe svg {\n            margin: 0 !important;\n            padding: 0 !important;\n            width: 20px !important;\n            height: 20px !important;\n            flex-shrink: 0 !important;\n        }\n        \n        /* Google button text positioning */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb .nsm7Bb-HzV7m-LgbsSe-Bz112c,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb span,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe-Bz112c,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe span {\n            margin: 0 !important;\n            padding: 0 !important;\n            font-size: 14px !important;\n            font-weight: 500 !important;\n            color: #000000 !important;\n            flex: none !important;\n        }\n\n        /* Personalized Google button specific styling to force full width */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe {\n            min-width: 100% !important;\n            max-width: 100% !important;\n            width: 100% !important;\n            box-sizing: border-box !important;\n        }\n\n        /* Force personalized Google button profile image to proper size */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe img,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-MJoBVe .nsm7Bb-HzV7m-LgbsSe {\n            width: 20px !important;\n            height: 20px !important;\n            border-radius: 50% !important;\n            flex-shrink: 0 !important;\n            margin: 0 !important;\n            padding: 0 !important;\n        }\n        \n        /* Aggressive Google styling removal */\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:hover,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:active,\n        .authiqa-container .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb *:focus {\n            box-shadow: none !important;\n            border: none !important;\n            outline: none !important;\n            transition: none !important;\n            transform: none !important;\n            margin: 0 !important;\n        }\n        \n        /* Label styling */\n        .authiqa-container .authiqa-label,\n        .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.5rem !important;\n            padding-left: 0.09rem !important;\n            font-weight: ').concat(at.labelFontWeight||"400"," !important;\n            color: ").concat(at.labelColor||(st?"#ffffff":it.inputText||"#333333")," !important;\n            font-size: ").concat(at.labelSize||"0.9rem",' !important;\n            height: auto !important;\n            line-height: 1.2 !important;\n        }\n        \n        /* Dark theme specific styles */\n        .authiqa-container[data-theme="dark"] .authiqa-label,\n        .authiqa-container[data-theme="dark"] label {\n            color: #ffffff !important;\n        }\n\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 1rem !important; /* Decreased from 1.5rem to 1.2rem (about 5px less) */\n        }\n\n        /* Ensure password container properly styles the label */\n        .authiqa-container .authiqa-password-container .authiqa-label {\n            display: block !important;\n            margin-bottom: 0.3rem !important; /* Changed to 0.3rem (approximately 5px) */\n            padding-left: 0.08rem !important; /* Added left padding to move labels slightly right */\n            font-weight: 500 !important;\n            height: 14px !important; /* Added fixed height */\n            line-height: 14px !important; /* Added line height to match height */\n        }\n\n        /* Password field container */\n        .authiqa-container .authiqa-password-container,\n        .authiqa-container .password-field-container {\n            position: relative !important;\n            width: 100% !important;\n        }\n\n        /* Password toggle button */\n        .authiqa-container .password-toggle {\n            position: absolute !important;\n            right: ').concat((null===(d=null===(l=this.customization)||void 0===l?void 0:l.passwordToggle)||void 0===d?void 0:d.rightOffset)||"12px"," !important;\n            top: ").concat((null===(u=this.customization.inputs)||void 0===u?void 0:u.height)?"calc(".concat(this.customization.inputs.height," / 2)"):"24px"," !important;\n            transform: translateY(-50%) !important;\n            background: none !important;\n            border: none !important;\n            color: ").concat((null===(p=null===(h=this.customization)||void 0===h?void 0:h.passwordToggle)||void 0===p?void 0:p.color)||it.inputText?it.inputText+"99":"#a1a1aa"," !important;\n            cursor: pointer !important;\n            padding: ").concat((null===(g=null===(m=this.customization)||void 0===m?void 0:m.passwordToggle)||void 0===g?void 0:g.padding)||"4px"," !important;\n            margin: 0 !important;\n            display: flex !important;\n            align-items: center !important;\n            justify-content: center !important;\n            height: 16px !important;\n            width: 16px !important;\n            z-index: 10 !important;\n            font-size: ").concat((null===(v=null===(f=this.customization)||void 0===f?void 0:f.passwordToggle)||void 0===v?void 0:v.size)||"14px"," !important;\n            line-height: 1 !important;\n            border-radius: 2px !important;\n        }\n\n        .authiqa-container .password-toggle:hover {\n            color: ").concat((null===(y=null===(b=this.customization)||void 0===b?void 0:b.passwordToggle)||void 0===y?void 0:y.hoverColor)||it.inputText||"#000000",' !important;\n        }\n        \n        /* Terms container - adjusted spacing */\n        .authiqa-container .terms-container {\n            display: flex !important;\n            align-items: flex-start !important;\n            margin: 0.5rem 0 1rem 0 !important; /* Decreased bottom margin from 3rem to 1rem */\n            position: relative !important;\n        }\n\n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin: 0.25rem 0.5rem 0 0 !important; /* Standardized margins */\n            position: static !important; /* Remove relative positioning */\n        }\n\n        .authiqa-container .terms-container label {\n            color: ').concat((null===(w=at.termsText)||void 0===w?void 0:w.textColor)||it.inputText||"#333333"," !important;\n            font-size: 0.875rem !important;\n            line-height: 1.4 !important;\n            margin: 0 !important;\n            padding-top: 0 !important;\n            margin-left: 0 !important;\n            flex: 1 !important;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat((null===(x=at.termsText)||void 0===x?void 0:x.linkColor)||it.buttonBackground||"#000000",' !important;\n            text-decoration: none !important;\n        }\n        \n        /* Input field styling - highest priority for user customization */\n        .authiqa-container input[type="text"],\n        .authiqa-container input[type="email"],\n        .authiqa-container input[type="password"],\n        .authiqa-input {\n            width: ').concat((null===(T=this.customization.inputs)||void 0===T?void 0:T.width)||"100%"," !important;\n            height: ").concat((null===(S=this.customization.inputs)||void 0===S?void 0:S.height)||"50px"," !important;\n            padding: ").concat((null===(P=this.customization.inputs)||void 0===P?void 0:P.padding)||"0 1rem"," !important;\n            font-size: ").concat((null===(C=this.customization.inputs)||void 0===C?void 0:C.fontSize)||"1rem"," !important;\n            font-weight: ").concat((null===(k=this.customization.inputs)||void 0===k?void 0:k.fontWeight)||"400"," !important;\n            border-radius: ").concat((null===(A=this.customization.inputs)||void 0===A?void 0:A.borderRadius)||"4px"," !important;\n            background-color: ").concat(it.inputBackground," !important;\n            color: ").concat(it.inputText," !important;\n            border: 1px solid ").concat(it.borderColor," !important;\n            margin: ").concat((null===(q=this.customization.inputs)||void 0===q?void 0:q.margin)||"0 0 1rem 0",' !important;\n            box-sizing: border-box !important;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;\n        }\n        .authiqa-container input[type="text"]:focus,\n        .authiqa-container input[type="email"]:focus,\n        .authiqa-container input[type="password"]:focus,\n        .authiqa-input:focus {\n            border-color: ').concat((null===(L=this.customization.inputs)||void 0===L?void 0:L.focusBorderColor)||"#000000"," !important;\n            box-shadow: ").concat((null===(z=this.customization.inputs)||void 0===z?void 0:z.focusBoxShadow)||"none",' !important;\n            outline: none !important;\n        }\n\n        /* Checkbox specific styling */\n        .authiqa-container input[type="checkbox"] {\n            width: auto !important;\n            height: auto !important;\n            margin-right: 8px !important;\n            margin-top: 3px !important;\n            background-color: transparent !important;\n        }\n\n        /* Decrease spacing between password field and terms */\n        .authiqa-container .authiqa-labeled-input {\n            margin-bottom: 0.5rem !important; /* Decreased from 1rem to 0.5rem */\n        }\n\n        /* Button spacing - no need to change as the terms container\'s bottom margin will create space */\n        .authiqa-container form button[type="submit"] {\n            margin-top: 0 !important; /* Remove top margin as we\'re using bottom margin on terms container */\n        }\n\n        .authiqa-container input[type="text"]::placeholder,\n        .authiqa-container input[type="email"]::placeholder,\n        .authiqa-container input[type="password"]::placeholder,\n        .authiqa-input::placeholder {\n            color: ').concat(it.inputPlaceholder||"#a3a3a3"," !important;\n            text-align: ").concat((null===(E=this.customization.inputs)||void 0===E?void 0:E.placeholderAlign)||"left"," !important;\n        }\n\n        /* Navigation (alternate-action) styling */\n        .authiqa-container .alternate-action {\n            text-align: ").concat((null===(I=this.customization.navLinks)||void 0===I?void 0:I.textAlign)||"center"," !important;\n            margin-top: ").concat((null===(B=this.customization.navLinks)||void 0===B?void 0:B.marginTop)||"1.5rem"," !important;\n            margin-bottom: ").concat((null===(M=this.customization.navLinks)||void 0===M?void 0:M.marginBottom)||"0"," !important;\n            font-size: ").concat((null===(R=this.customization.navLinks)||void 0===R?void 0:R.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(_=this.customization.navLinks)||void 0===_?void 0:_.color)||"var(--authiqa-nav-text-color, #1a1a1a)"," !important;\n            font-family: ").concat((null===(N=this.customization.navLinks)||void 0===N?void 0:N.fontFamily)||at.fontFamily," !important;\n            font-weight: ").concat((null===(O=this.customization.navLinks)||void 0===O?void 0:O.fontWeight)||"400",' !important;\n        }\n        .authiqa-container[data-theme="dark"] .alternate-action {\n            color: var(--authiqa-nav-text-color-dark, #ffffff) !important;\n        }\n\n        ').concat((null===(U=this.customization.navLinks)||void 0===U?void 0:U.secondaryButtonStyle)?"\n        /* Secondary button styling for navigation links */\n        .authiqa-container .alternate-action a {\n            display: inline-block !important;\n            background-color: ".concat((null===(H=this.customization.navLinks)||void 0===H?void 0:H.secondaryButtonBackground)||"#42b883"," !important;\n            color: ").concat((null===(j=this.customization.navLinks)||void 0===j?void 0:j.secondaryButtonText)||"#ffffff"," !important;\n            padding: ").concat((null===(V=this.customization.navLinks)||void 0===V?void 0:V.secondaryButtonPadding)||"0.75rem 1.5rem"," !important;\n            border-radius: ").concat((null===(F=this.customization.navLinks)||void 0===F?void 0:F.secondaryButtonBorderRadius)||"6px"," !important;\n            height: ").concat((null===(D=this.customization.navLinks)||void 0===D?void 0:D.secondaryButtonHeight)||"auto"," !important;\n            text-decoration: none !important;\n            font-weight: ").concat((null===(G=this.customization.navLinks)||void 0===G?void 0:G.linkFontWeight)||"500"," !important;\n            transition: background-color 0.2s ease !important;\n            border: none !important;\n            cursor: pointer !important;\n            text-align: center !important;\n            line-height: 1 !important;\n        }\n        .authiqa-container .alternate-action a:hover {\n            background-color: ").concat((null===(W=this.customization.navLinks)||void 0===W?void 0:W.secondaryButtonHover)||"#369870"," !important;\n            text-decoration: none !important;\n        }\n        "):"\n        /* Default link styling for navigation */\n        .authiqa-container .alternate-action a {\n            color: ".concat((null===(K=this.customization.navLinks)||void 0===K?void 0:K.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(J=this.customization.navLinks)||void 0===J?void 0:J.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .alternate-action a:hover {\n            text-decoration: underline !important;\n        }\n        "),"\n        /* Forgot password link styling */\n        .authiqa-container .forgot-password {\n            text-align: ").concat((null===(Y=this.customization.navLinks)||void 0===Y?void 0:Y.textAlign)||"right"," !important;\n            margin-top: -1rem !important;\n            margin-bottom: 1rem !important;\n            font-size: ").concat((null===(X=this.customization.navLinks)||void 0===X?void 0:X.fontSize)||"0.95rem"," !important;\n            color: ").concat((null===(Z=this.customization.navLinks)||void 0===Z?void 0:Z.color)||"#525252"," !important;\n            font-family: ").concat((null===($=this.customization.navLinks)||void 0===$?void 0:$.fontFamily)||at.fontFamily," !important;\n            font-weight: ").concat((null===(Q=this.customization.navLinks)||void 0===Q?void 0:Q.fontWeight)||"400"," !important;\n        }\n        .authiqa-container .forgot-password a {\n            color: ").concat((null===(tt=this.customization.navLinks)||void 0===tt?void 0:tt.linkColor)||"#0070f3"," !important;\n            font-weight: ").concat((null===(et=this.customization.navLinks)||void 0===et?void 0:et.linkFontWeight)||"500"," !important;\n            text-decoration: none !important;\n        }\n        .authiqa-container .forgot-password a:hover {\n            text-decoration: underline !important;\n        }\n    ")},t}();e.StyleGenerator=a},92:(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.generateTermsContainerStyles=e.getComponentStyles=e.getStyleContent=void 0;var o=n(149);e.getStyleContent=function(t){return"\n        /* Dynamically generated styles for ".concat(t," theme */\n        :root {\n            --authiqa-bg-color: ").concat("dark"===t?"#18181b":"#ffffff",";\n            --authiqa-text-color: ").concat("dark"===t?"#ffffff":"#1a1a1a",";\n            --authiqa-border-color: ").concat("dark"===t?"#3f3f46":"#e5e5e5",";\n            --authiqa-input-bg: ").concat("dark"===t?"#27272a":"#ffffff",";\n            --authiqa-button-bg: ").concat("dark"===t?"#ffffff":"#18181b",";\n            --authiqa-button-text: ").concat("dark"===t?"#18181b":"#ffffff",";\n        }\n    ")},e.getComponentStyles=function(t){void 0===t&&(t="light");var e=o.THEMES[t];return{modal:{overlay:{position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:e.modalOverlay,zIndex:1e3},container:{position:"relative",width:"500px",margin:"50px auto",backgroundColor:e.background,color:e.text,borderRadius:"8px",padding:"20px",border:"1px solid ".concat(e.border)}},iframe:{border:"none",width:"100%",height:"600px",backgroundColor:e.background},message:{position:"fixed",top:"20px",left:"50%",transform:"translateX(-50%)",padding:"12px 24px",borderRadius:"4px",fontSize:"14px",fontWeight:"500",opacity:"0",transition:"opacity 0.3s ease"},messageSuccess:{backgroundColor:"#4CAF50",color:"white"},messageError:{backgroundColor:"#f44336",color:"white"},messageShow:{opacity:"1"}}},e.generateTermsContainerStyles=function(t){var e;if(!(null===(e=t.customization)||void 0===e?void 0:e.colors))return"";var n=t.customization.colors;return'\n        /* Terms container styling */\n        .authiqa-container .terms-container {\n            display: flex;\n            align-items: flex-start;\n            margin: 0.75rem 0;\n        }\n        \n        .authiqa-container .terms-container input[type="checkbox"] {\n            margin-top: 3px;\n            margin-right: 8px;\n        }\n        \n        .authiqa-container .terms-container label {\n            color: '.concat(n.inputText||"#333333",";\n            font-size: 0.875rem;\n            line-height: 1.4;\n            margin: 0;\n            flex: 1;\n        }\n        \n        .authiqa-container .terms-container a {\n            color: ").concat(n.buttonBackground||"#000000",";\n            text-decoration: none;\n        }\n        \n        .authiqa-container .terms-container a:hover {\n            text-decoration: underline;\n        }\n    ")}},733:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.validateCustomAuthPaths=e.validateAuthUrls=void 0,e.validateAuthUrls=function(t,e){if(!t)return{isValid:!1,message:"Authentication URLs are required"};for(var n=0,o=["signup","signin","verify","reset","update","resend","successful"];n<o.length;n++){var i=o[n];if(!t[i])return{isValid:!1,message:"".concat(i," is required")};try{if("https:"!==new URL(t[i]).protocol)return{isValid:!1,message:"".concat(i," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"Invalid URL format for ".concat(i)}}}return{isValid:!0,message:""}},e.validateCustomAuthPaths=function(t,e){for(var n=0,o=Object.entries(t);n<o.length;n++){var i=o[n],a=i[0],r=i[1];if(r)try{if("https:"!==new URL(r).protocol)return{isValid:!1,message:"".concat(a," must use HTTPS protocol")}}catch(t){return{isValid:!1,message:"".concat(a," must be a complete URL (e.g., https://domain.com/path)")}}}return{isValid:!0,message:""}}}},e={};function n(o){var i=e[o];if(void 0!==i)return i.exports;var a=e[o]={id:o,exports:{}};return t[o].call(a.exports,a,a.exports,n),a.exports}return n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nc=void 0,n(156)})()));