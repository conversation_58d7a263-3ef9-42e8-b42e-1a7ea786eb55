import { PresetDesign, PresetName, PresetManager as IPresetManager } from './preset-types';
import { WidgetCustomization } from './customization-types';
import { 
  modernPreset, 
  classicPreset, 
  minimalPreset, 
  corporatePreset 
} from './preset-designs';
import {
  gradientPreset,
  roundedPreset,
  sharpPreset
} from './preset-designs-extended';

/**
 * Deep merge utility function for combining customization objects
 */
function deepMerge(target: any, source: any): any {
  const result = { ...target };

  for (const key in source) {
    if (source[key] !== undefined && source[key] !== null) {
      if (typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }

  return result;
}

export class PresetManager implements IPresetManager {
  private presets: Map<PresetName, PresetDesign> = new Map();

  constructor() {
    this.initializePresets();
  }

  private initializePresets(): void {
    // Register all available presets
    const allPresets = [
      modernPreset,
      classicPreset,
      minimalPreset,
      corporatePreset,
      gradientPreset,
      roundedPreset,
      sharpPreset
    ];

    allPresets.forEach(preset => {
      this.presets.set(preset.name, preset);
    });
  }

  /**
   * Get a specific preset by name
   */
  getPreset(name: PresetName): PresetDesign | null {
    return this.presets.get(name) || null;
  }

  /**
   * Get all available presets
   */
  getAllPresets(): PresetDesign[] {
    return Array.from(this.presets.values());
  }

  /**
   * Get presets filtered by category
   */
  getPresetsByCategory(category: string): PresetDesign[] {
    return this.getAllPresets().filter(preset => preset.category === category);
  }

  /**
   * Merge a preset with custom customization overrides
   */
  mergePresetWithCustomization(
    preset: PresetDesign, 
    customization?: Partial<WidgetCustomization>
  ): WidgetCustomization {
    if (!customization) {
      return preset.customization;
    }

    return deepMerge(preset.customization, customization);
  }

  /**
   * Apply a preset with optional custom overrides
   */
  applyPreset(
    name: PresetName, 
    customOverrides?: Partial<WidgetCustomization>
  ): WidgetCustomization {
    const preset = this.getPreset(name);
    
    if (!preset) {
      throw new Error(`Preset "${name}" not found`);
    }

    return this.mergePresetWithCustomization(preset, customOverrides);
  }

  /**
   * Get preset categories
   */
  getCategories(): string[] {
    const categories = new Set<string>();
    this.getAllPresets().forEach(preset => {
      categories.add(preset.category);
    });
    return Array.from(categories);
  }

  /**
   * Check if a preset exists
   */
  hasPreset(name: PresetName): boolean {
    return this.presets.has(name);
  }

  /**
   * Get preset preview information
   */
  getPresetPreview(name: PresetName): PresetDesign['preview'] | null {
    const preset = this.getPreset(name);
    return preset?.preview || null;
  }

  /**
   * Register a new custom preset
   */
  registerPreset(preset: PresetDesign): void {
    this.presets.set(preset.name, preset);
  }

  /**
   * Remove a preset
   */
  removePreset(name: PresetName): boolean {
    return this.presets.delete(name);
  }

  /**
   * Get preset metadata (name, displayName, description, category)
   */
  getPresetMetadata(name: PresetName): Omit<PresetDesign, 'customization'> | null {
    const preset = this.getPreset(name);
    if (!preset) return null;

    const { customization, ...metadata } = preset;
    return metadata;
  }

  /**
   * Search presets by name or description
   */
  searchPresets(query: string): PresetDesign[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllPresets().filter(preset => 
      preset.name.toLowerCase().includes(lowercaseQuery) ||
      preset.displayName.toLowerCase().includes(lowercaseQuery) ||
      preset.description.toLowerCase().includes(lowercaseQuery)
    );
  }
}

// Export singleton instance
export const presetManager = new PresetManager();
